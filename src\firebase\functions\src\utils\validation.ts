import * as functions from 'firebase-functions';

/**
 * Interface pour les données d'emprunt
 */
export interface EmpruntData {
  nom: string;
  lieu: string;
  dateDepart: Date;
  dateRetourPrevue: Date;
  secteur: string;
  referent: string;
  emprunteur: string;
  notes?: string;
  materiel?: Array<{
    idMateriel: string;
    type: 'module' | 'stock';
    quantite: number;
  }>;
}

/**
 * Valide les données d'un emprunt
 */
export function validateEmpruntData(data: any): EmpruntData {
  if (!data || typeof data !== 'object') {
    throw new functions.https.HttpsError('invalid-argument', 'Données invalides');
  }

  const required = ['nom', 'lieu', 'dateDepart', 'dateRetourPrevue', 'secteur', 'referent', 'emprunteur'];
  for (const field of required) {
    if (!data[field] || typeof data[field] !== 'string') {
      throw new functions.https.HttpsError('invalid-argument', `Champ requis manquant ou invalide: ${field}`);
    }
  }

  // Validation des dates
  const dateDepart = new Date(data.dateDepart);
  const dateRetourPrevue = new Date(data.dateRetourPrevue);
  
  if (isNaN(dateDepart.getTime()) || isNaN(dateRetourPrevue.getTime())) {
    throw new functions.https.HttpsError('invalid-argument', 'Dates invalides');
  }

  if (dateRetourPrevue <= dateDepart) {
    throw new functions.https.HttpsError('invalid-argument', 'La date de retour doit être postérieure à la date de départ');
  }

  return {
    nom: data.nom.trim(),
    lieu: data.lieu.trim(),
    dateDepart,
    dateRetourPrevue,
    secteur: data.secteur.trim(),
    referent: data.referent.trim(),
    emprunteur: data.emprunteur.trim(),
    notes: data.notes ? data.notes.trim() : '',
    materiel: data.materiel || []
  };
}
