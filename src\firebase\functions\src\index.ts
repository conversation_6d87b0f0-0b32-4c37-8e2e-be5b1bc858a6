import * as admin from 'firebase-admin';

/**
 * 🚀 Initialisation du SDK Firebase Admin.
 *
 * On vérifie si une instance de l'application Firebase (admin) a déjà été initialisée.
 * Si ce n'est pas le cas (longueur du tableau `admin.apps` est 0), on l'initialise.
 *
 * Cette approche, connue sous le nom de "lazy initialization", est cruciale pour
 * éviter les timeouts au démarrage de l'émulateur ou lors du déploiement.
 * Elle garantit que l'initialisation ne se produit qu'une seule fois et uniquement
 * lorsque c'est nécessaire, sans bloquer le processus de découverte des fonctions.
 */
if (!admin.apps.length) {
  admin.initializeApp();
}

// ======================================================================================
// 🔥 EXPORTATION DES CLOUD FUNCTIONS
//
// Chaque fichier exporté ci-dessous contient la logique pour une ou plusieurs
// Cloud Functions. Le fait de les regrouper par fonctionnalité (auth, etc.)
// permet de garder le code organisé et maintenable.
// ======================================================================================

// --- Fonctions liées à l'authentification et à la gestion des utilisateurs ---
export * from './auth/onUserCreate';
export * from './auth/setUserRole';
export * from './auth/userManagement'; // Assurez-vous que le nom du fichier correspond

// --- Autres fonctions (vous pouvez en ajouter ici) ---
// export * from './notifications/sendWelcomeEmail';
// export * from './database/cleanOldDocuments';