// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Configuration globale pour les tests SIGMA
Cypress.on('uncaught:exception', (err, runnable) => {
  // Ignorer certaines erreurs non critiques
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false
  }
  return true
})

// Configuration des timeouts
Cypress.config('defaultCommandTimeout', 10000)
Cypress.config('requestTimeout', 10000)
Cypress.config('responseTimeout', 10000)

// Mock des services Firebase pour les tests
beforeEach(() => {
  // Mock Firebase Auth
  cy.window().then((win) => {
    win.firebase = {
      auth: () => ({
        currentUser: {
          uid: 'test-user',
          email: '<EMAIL>',
          getIdTokenResult: () => Promise.resolve({
            claims: { role: 'regisseur' }
          })
        },
        onAuthStateChanged: (callback) => {
          callback({
            uid: 'test-user',
            email: '<EMAIL>'
          })
        }
      }),
      firestore: () => ({
        collection: () => ({
          doc: () => ({
            get: () => Promise.resolve({
              exists: true,
              data: () => ({})
            }),
            set: () => Promise.resolve(),
            update: () => Promise.resolve()
          }),
          add: () => Promise.resolve({ id: 'test-doc-id' }),
          where: () => ({
            get: () => Promise.resolve({
              docs: []
            })
          })
        })
      }),
      functions: () => ({
        httpsCallable: (name) => (data) => {
          // Mock des Cloud Functions
          switch (name) {
            case 'createEmprunt':
              return Promise.resolve({
                data: {
                  success: true,
                  emprunt: { id: 'test-emprunt-id', ...data }
                }
              })
            case 'updateEmpruntStatus':
              return Promise.resolve({
                data: { success: true }
              })
            case 'generateEmpruntLabels':
              return Promise.resolve({
                data: {
                  success: true,
                  pdf: 'base64-pdf-data',
                  filename: 'test.pdf'
                }
              })
            default:
              return Promise.resolve({ data: { success: true } })
          }
        }
      })
    }
  })
})
