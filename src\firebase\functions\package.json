{"name": "functions", "version": "1.0.0", "main": "lib/index.js", "scripts": {"build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "engines": {"node": "18"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"firebase-admin": "^13.4.0", "firebase-functions": "^6.4.0", "pdf-lib": "^1.17.1"}, "devDependencies": {"@firebase/rules-unit-testing": "^5.0.0", "@types/jest": "^30.0.0", "@types/node": "^24.2.0", "firebase-functions-test": "^3.4.1", "jest": "^30.0.5", "ts-jest": "^29.4.1", "typescript": "^5.9.2"}}