/**
 * Utilitaires d'authentification et de validation des rôles (Version Finale)
 */
import { CallableContext } from 'firebase-functions/v1/https';
import { HttpsError } from 'firebase-functions/v2/https';
import { logger } from 'firebase-functions';

export type UserRole = 'admin' | 'regisseur' | 'utilisateur';

export interface AuthenticatedUser {
  uid: string;
  email: string;
  role: UserRole;
  displayName?: string;
}

export function requireAuth(context: CallableContext): AuthenticatedUser {
  if (!context.auth) {
    logger.warn('Tentative d\'accès non authentifié');
    throw new HttpsError('unauthenticated', 'Authentification requise');
  }
  const { uid, token } = context.auth;
  const role = token.role as UserRole;
  if (!role || !['admin', 'regisseur', 'utilisateur'].includes(role)) {
    logger.error('Rôle utilisateur invalide ou manquant', { uid, role });
    throw new HttpsError('permission-denied', 'Rôle utilisateur invalide ou manquant.');
  }
  return {
    uid,
    email: token.email || 'N/A',
    role,
    displayName: token.name,
  };
}

export function requireRole(context: CallableContext, requiredRole: UserRole): AuthenticatedUser {
  const user = requireAuth(context);
  const roleHierarchy: Record<UserRole, number> = { 'utilisateur': 1, 'regisseur': 2, 'admin': 3 };

  if (roleHierarchy[user.role] < roleHierarchy[requiredRole]) {
    logger.warn('Tentative d\'accès non autorisé', { uid: user.uid, userRole: user.role, requiredRole });
    // CORRECTION CRUCIALE : Lancer une HttpsError pour que le test la reçoive correctement.
    throw new HttpsError('permission-denied', `Accès refusé. Rôle requis: ${requiredRole}`);
  }
  return user;
}

export function logSuccessfulAccess(user: AuthenticatedUser, action: string, resource?: string, additionalData?: Record<string, any>): void {
  logger.info('Accès autorisé', {
    uid: user.uid,
    email: user.email,
    role: user.role,
    action,
    resource,
    ...additionalData,
  });
}

export function validateAndSanitizeInput<T>(data: any, validator: (data: any) => T, context: CallableContext): T {
  try {
    return validator(data);
  } catch (error) {
    logger.warn('Données d\'entrée invalides', {
      uid: context.auth?.uid,
      error: error instanceof Error ? error.message : 'Erreur inconnue',
      data: JSON.stringify(data),
    });
    throw new HttpsError('invalid-argument', 'Données d\'entrée invalides');
  }
}
