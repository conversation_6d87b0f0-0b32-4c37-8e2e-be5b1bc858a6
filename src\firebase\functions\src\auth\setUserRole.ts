// functions/src/auth/setUserRole.ts
import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { logger } from 'firebase-functions';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';

const ALLOWED_ROLES = new Set(['utilisateur', 'regisseur', 'admin']);
const HTTPS_ERROR_CODES = new Set([
  'cancelled','unknown','invalid-argument','deadline-exceeded','not-found','already-exists',
  'permission-denied','resource-exhausted','failed-precondition','aborted','out-of-range',
  'unimplemented','internal','unavailable','data-loss','unauthenticated'
]);

export const setUserRole = onCall({ region: 'europe-west1' }, async (request) => {
  try {
    // 1) Auth requise
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'Authentification requise.');
    }

    // 2) Vérif du rôle appelant (doit être admin)
    const requesterUid = request.auth.uid;
    const requesterRole = String(request.auth.token?.role ?? 'utilisateur');
    if (requesterRole !== 'admin') {
      logger.warn('Tentative d’accès non autorisé', {
        uid: requesterUid,
        userRole: requesterRole,
        requiredRole: 'admin',
      });
      throw new HttpsError('permission-denied', 'Accès refusé. Rôle requis: admin');
    }

    // 3) Validation input
    const userId = String(request.data?.userId ?? request.data?.uid ?? '').trim();
    const role = String(request.data?.role ?? '').trim();
    if (!userId) throw new HttpsError('invalid-argument', "Paramètre 'userId' requis.");
    if (!role || !ALLOWED_ROLES.has(role)) {
      throw new HttpsError('invalid-argument', "Paramètre 'role' invalide.");
    }

    // 4) Opérations Auth + Firestore
    const auth = getAuth();
    await auth.getUser(userId); // vérifie existence

    await auth.setCustomUserClaims(userId, { role });

    const db = getFirestore();
    await db.collection('users').doc(userId).set({ role }, { merge: true });

    // 5) Réponse OK
    return { success: true, newRole: role };
  } catch (err: any) {
    logger.error('Erreur dans setUserRole', {
      code: err?.code ?? err?.errorInfo?.code,
      message: err?.message,
      stack: err?.stack,
    });

    // Si c'est déjà un HttpsError (code connu), ne pas le remapper en "internal"
    if (typeof err?.code === 'string' && HTTPS_ERROR_CODES.has(err.code)) {
      throw err;
    }

    // Mapping d'erreurs Admin SDK utiles
    const adminCode = err?.errorInfo?.code || err?.code;
    if (adminCode === 'auth/user-not-found') {
      throw new HttpsError('not-found', 'Utilisateur introuvable.');
    }

    // Fallback générique
    throw new HttpsError('internal', 'Erreur interne du serveur');
  }
});
