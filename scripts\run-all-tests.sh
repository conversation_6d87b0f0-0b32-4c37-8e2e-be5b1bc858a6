#!/bin/bash
# Script simplifié pour exécuter tous les tests via 'firebase emulators:exec'
# Cette commande gère le démarrage, l'exécution des tests et l'arrêt des émulateurs.

set -e # Arrêter en cas d'erreur

# Couleurs et Fonctions d'Affichage
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }

echo "🧪 Démarrage de la suite complète de tests SIGMA via emulators:exec"
echo "=================================================================="

log_info "Installation des dépendances et build des Cloud Functions..."
# Le pré-requis est d'avoir les fonctions buildées
cd src/firebase/functions
npm install > /dev/null
npm run build
cd - > /dev/null
log_success "Dépendances installées et code compilé."

log_info "Lancement de 'firebase emulators:exec'. Cette commande va démarrer les émulateurs, lancer Jest, puis tout arrêter..."

# Exécution des tests via le wrapper de Firebase
# --only précise les émulateurs à lancer
# 'npm test' est la commande à exécuter. --runInBand est ajouté au script de test pour la stabilité.
if firebase emulators:exec --only auth,firestore,functions "cd src/firebase/functions && npm test"; then
    log_success "🎉 TOUS LES TESTS SONT RÉUSSIS !"
else
    # L'erreur est déjà affichée par la commande qui a échoué
    exit 1
fi

exit 0
