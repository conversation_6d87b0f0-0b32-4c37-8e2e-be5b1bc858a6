/**
 * Tests unitaires pour les Cloud Functions d'authentification (émulateurs).
 * - Aligne la région client (europe-west1) avec la région Functions
 * - Connecte l'instance regionnée au Functions Emulator
 * - Corrige l'assertion non-admin (code 'functions/permission-denied')
 * - Rend onUserCreate robuste via polling
 */
import { describe, it, beforeAll, afterAll, beforeEach, expect } from '@jest/globals';
import { initializeTestEnvironment, RulesTestEnvironment } from '@firebase/rules-unit-testing';
import { getAuth as getAdminAuth } from 'firebase-admin/auth';
import { getFirestore as getAdminFirestore } from 'firebase-admin/firestore';

import { httpsCallable, getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import { initializeApp, getApps, deleteApp, FirebaseApp } from 'firebase/app';
import { getAuth as getClientAuth, signInWithCustomToken, connectAuthEmulator } from 'firebase/auth';

const PROJECT_ID = process.env.GCLOUD_PROJECT || 'sigma-nova';

let testEnv: RulesTestEnvironment;
let adminAuth: ReturnType<typeof getAdminAuth>;
let adminFirestore: ReturnType<typeof getAdminFirestore>;
let testApp: FirebaseApp;

const testUsers = {
  admin: { uid: 'test-admin-uid', email: '<EMAIL>', displayName: 'Admin', role: 'admin' as const },
  user:  { uid: 'test-user-uid',  email: '<EMAIL>', displayName: 'User',  role: 'utilisateur' as const },
};

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: PROJECT_ID,
    firestore: {
      host: '127.0.0.1',
      port: 8080,
      rules: `
        rules_version = '2';
        service cloud.firestore {
          match /databases/{database}/documents {
            match /{document=**} {
              allow read, write: if true; // tests uniquement
            }
          }
        }
      `,
    },
  });

  adminAuth = getAdminAuth();
  adminFirestore = getAdminFirestore();

  // Nettoyer d'anciennes apps client éventuelles
  getApps().forEach(app => deleteApp(app));

  testApp = initializeApp({
    apiKey: 'fake',
    projectId: PROJECT_ID,
    authDomain: `${PROJECT_ID}.firebaseapp.com`,
    storageBucket: `${PROJECT_ID}.appspot.com`,
  });

  connectAuthEmulator(getClientAuth(testApp), 'http://127.0.0.1:9099', { disableWarnings: true });
}, 30_000);

afterAll(async () => {
  await testEnv.cleanup();
  const apps = getApps();
  await Promise.all(apps.map(app => deleteApp(app)));
}, 30_000);

beforeEach(async () => {
  // Reset Firestore
  await testEnv.clearFirestore();

  // (Ré)initialisation idempotente des utilisateurs dans l'émulateur Auth
  async function ensureUser(uid: string, email: string, displayName: string) {
    try {
      await adminAuth.createUser({ uid, email, displayName });
    } catch (e: any) {
      // Ignore si déjà existant
      if (e?.errorInfo?.code !== 'auth/uid-already-exists') throw e;
    }
  }
  await Promise.all([
    ensureUser(testUsers.admin.uid, testUsers.admin.email, testUsers.admin.displayName),
    ensureUser(testUsers.user.uid,  testUsers.user.email,  testUsers.user.displayName),
  ]);

  // Claims initiaux
  await adminAuth.setCustomUserClaims(testUsers.admin.uid, { role: testUsers.admin.role });
  await adminAuth.setCustomUserClaims(testUsers.user.uid,  { role: testUsers.user.role  });

  // Docs Firestore initiaux (sauf pour le futur "new user")
  await adminFirestore.collection('users').doc(testUsers.admin.uid).set({
    email: testUsers.admin.email, displayName: testUsers.admin.displayName, role: testUsers.admin.role, createdAt: new Date(),
  });
  await adminFirestore.collection('users').doc(testUsers.user.uid).set({
    email: testUsers.user.email, displayName: testUsers.user.displayName, role: testUsers.user.role, createdAt: new Date(),
  });
}, 30_000);

// ---- Helper : instance Functions authentifiée, regionnée et connectée
async function getAuthenticatedFunctions(user: { uid: string; role?: string }) {
  const auth = getClientAuth(testApp);
  const token = await adminAuth.createCustomToken(user.uid, user.role ? { role: user.role } : undefined);
  await signInWithCustomToken(auth, token);

  const funcs = getFunctions(testApp, 'europe-west1');
  connectFunctionsEmulator(funcs, '127.0.0.1', 5001);
  return funcs;
}

describe("Cloud Functions d'Authentification", () => {
  describe('setUserRole', () => {
    it("devrait permettre à un admin d'assigner un rôle", async () => {
      const functions = await getAuthenticatedFunctions(testUsers.admin);
      const setUserRole = httpsCallable(functions, 'setUserRole');

      const res = await setUserRole({ userId: testUsers.user.uid, role: 'regisseur' });
      const data = res.data as any;

      expect(data?.success).toBe(true);
      expect(data?.newRole).toBe('regisseur');

      const updated = await adminAuth.getUser(testUsers.user.uid);
      expect(updated.customClaims?.role).toBe('regisseur');
    }, 30_000);

    it("devrait refuser l'accès à un non-admin", async () => {
      const functions = await getAuthenticatedFunctions(testUsers.user);
      const setUserRole = httpsCallable(functions, 'setUserRole');

      await expect(setUserRole({ userId: testUsers.admin.uid, role: 'utilisateur' }))
        .rejects.toMatchObject({ code: 'functions/permission-denied' }); // vérifie le code
    }, 30_000);
  });

  describe('onUserCreate', () => {
    it('devrait assigner le rôle utilisateur par défaut', async () => {
      // uid unique par run
      const NEW_UID = `test-new-user-uid-${Date.now()}`;
      await adminAuth.createUser({ uid: NEW_UID, email: '<EMAIL>' });

      // Polling pour laisser le trigger finir : claims + doc Firestore
      const deadline = Date.now() + 10_000;
      let role: string | undefined;
      let hasDoc = false;

      while (Date.now() < deadline) {
        const rec = await adminAuth.getUser(NEW_UID).catch(() => null);
        role = rec?.customClaims?.role as string | undefined;

        const doc = await adminFirestore.collection('users').doc(NEW_UID).get();
        hasDoc = doc.exists && doc.data()?.role === 'utilisateur';

        if (role === 'utilisateur' && hasDoc) break;
        await new Promise(r => setTimeout(r, 200));
      }

      expect(role).toBe('utilisateur');
      expect(hasDoc).toBe(true);
    }, 30_000);
  });
});
