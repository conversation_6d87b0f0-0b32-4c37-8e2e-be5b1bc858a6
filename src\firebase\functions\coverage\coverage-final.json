{"C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\index.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 22}}, "2": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 41}}, "3": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 47}}, "4": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 49}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\emprunts\\createEmprunt.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\emprunts\\createEmprunt.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 54}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 58}}, "4": {"start": {"line": 6, "column": 11}, "end": {"line": 6, "column": 28}}, "5": {"start": {"line": 12, "column": 13}, "end": {"line": 109, "column": 3}}, "6": {"start": {"line": 13, "column": 2}, "end": {"line": 108, "column": 3}}, "7": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 35}}, "8": {"start": {"line": 18, "column": 26}, "end": {"line": 18, "column": 51}}, "9": {"start": {"line": 21, "column": 19}, "end": {"line": 85, "column": 6}}, "10": {"start": {"line": 23, "column": 25}, "end": {"line": 23, "column": 56}}, "11": {"start": {"line": 26, "column": 6}, "end": {"line": 28, "column": 7}}, "12": {"start": {"line": 27, "column": 8}, "end": {"line": 27, "column": 80}}, "13": {"start": {"line": 31, "column": 25}, "end": {"line": 47, "column": 8}}, "14": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 46}}, "15": {"start": {"line": 53, "column": 6}, "end": {"line": 70, "column": 7}}, "16": {"start": {"line": 54, "column": 8}, "end": {"line": 69, "column": 9}}, "17": {"start": {"line": 55, "column": 30}, "end": {"line": 55, "column": 69}}, "18": {"start": {"line": 56, "column": 10}, "end": {"line": 63, "column": 13}}, "19": {"start": {"line": 66, "column": 10}, "end": {"line": 68, "column": 11}}, "20": {"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 118}}, "21": {"start": {"line": 73, "column": 28}, "end": {"line": 73, "column": 69}}, "22": {"start": {"line": 74, "column": 6}, "end": {"line": 79, "column": 9}}, "23": {"start": {"line": 81, "column": 6}, "end": {"line": 83, "column": null}}, "24": {"start": {"line": 87, "column": 4}, "end": {"line": 90, "column": 7}}, "25": {"start": {"line": 92, "column": 4}, "end": {"line": 95, "column": 6}}, "26": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 79}}, "27": {"start": {"line": 100, "column": 4}, "end": {"line": 102, "column": 5}}, "28": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 18}}, "29": {"start": {"line": 104, "column": 4}, "end": {"line": 107, "column": 6}}, "30": {"start": {"line": 118, "column": 2}, "end": {"line": 158, "column": 3}}, "31": {"start": {"line": 119, "column": 4}, "end": {"line": 157, "column": 5}}, "32": {"start": {"line": 121, "column": 24}, "end": {"line": 121, "column": 69}}, "33": {"start": {"line": 122, "column": 24}, "end": {"line": 122, "column": 56}}, "34": {"start": {"line": 124, "column": 6}, "end": {"line": 129, "column": 7}}, "35": {"start": {"line": 125, "column": 8}, "end": {"line": 128, "column": 10}}, "36": {"start": {"line": 131, "column": 25}, "end": {"line": 131, "column": 42}}, "37": {"start": {"line": 132, "column": 6}, "end": {"line": 137, "column": 7}}, "38": {"start": {"line": 133, "column": 8}, "end": {"line": 136, "column": 10}}, "39": {"start": {"line": 138, "column": 11}, "end": {"line": 157, "column": 5}}, "40": {"start": {"line": 140, "column": 23}, "end": {"line": 140, "column": 67}}, "41": {"start": {"line": 141, "column": 23}, "end": {"line": 141, "column": 54}}, "42": {"start": {"line": 143, "column": 6}, "end": {"line": 148, "column": 7}}, "43": {"start": {"line": 144, "column": 8}, "end": {"line": 147, "column": 10}}, "44": {"start": {"line": 150, "column": 24}, "end": {"line": 150, "column": 40}}, "45": {"start": {"line": 151, "column": 6}, "end": {"line": 156, "column": 7}}, "46": {"start": {"line": 152, "column": 8}, "end": {"line": 155, "column": 10}}, "47": {"start": {"line": 171, "column": 19}, "end": {"line": 171, "column": 55}}, "48": {"start": {"line": 172, "column": 19}, "end": {"line": 172, "column": 50}}, "49": {"start": {"line": 174, "column": 2}, "end": {"line": 191, "column": 3}}, "50": {"start": {"line": 175, "column": 28}, "end": {"line": 175, "column": 53}}, "51": {"start": {"line": 176, "column": 4}, "end": {"line": 179, "column": 7}}, "52": {"start": {"line": 182, "column": 25}, "end": {"line": 182, "column": 64}}, "53": {"start": {"line": 183, "column": 4}, "end": {"line": 190, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 52}, "end": {"line": 12, "column": 57}}, "loc": {"start": {"line": 12, "column": 86}, "end": {"line": 109, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 21, "column": 43}, "end": {"line": 21, "column": 48}}, "loc": {"start": {"line": 21, "column": 65}, "end": {"line": 85, "column": 5}}}, "2": {"name": "validateMaterielAvailability", "decl": {"start": {"line": 114, "column": 15}, "end": {"line": 114, "column": 43}}, "loc": {"start": {"line": 116, "column": 85}, "end": {"line": 159, "column": 1}}}, "3": {"name": "updateStockQuantity", "decl": {"start": {"line": 164, "column": 15}, "end": {"line": 164, "column": 34}}, "loc": {"start": {"line": 169, "column": 17}, "end": {"line": 192, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 6}, "end": {"line": 28, "column": 7}}, "type": "if", "locations": [{"start": {"line": 26, "column": 6}, "end": {"line": 28, "column": 7}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 26, "column": 10}, "end": {"line": 26, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 10}, "end": {"line": 26, "column": 32}}, {"start": {"line": 26, "column": 36}, "end": {"line": 26, "column": 69}}]}, "2": {"loc": {"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 34}}, {"start": {"line": 40, "column": 38}, "end": {"line": 40, "column": 40}}]}, "3": {"loc": {"start": {"line": 53, "column": 6}, "end": {"line": 70, "column": 7}}, "type": "if", "locations": [{"start": {"line": 53, "column": 6}, "end": {"line": 70, "column": 7}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 53, "column": 10}, "end": {"line": 53, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 10}, "end": {"line": 53, "column": 32}}, {"start": {"line": 53, "column": 36}, "end": {"line": 53, "column": 69}}]}, "5": {"loc": {"start": {"line": 66, "column": 10}, "end": {"line": 68, "column": 11}}, "type": "if", "locations": [{"start": {"line": 66, "column": 10}, "end": {"line": 68, "column": 11}}, {"start": {}, "end": {}}]}, "6": {"loc": {"start": {"line": 78, "column": 35}, "end": {"line": 78, "column": 85}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 35}, "end": {"line": 78, "column": 60}}, {"start": {"line": 78, "column": 64}, "end": {"line": 78, "column": 85}}]}, "7": {"loc": {"start": {"line": 100, "column": 4}, "end": {"line": 102, "column": 5}}, "type": "if", "locations": [{"start": {"line": 100, "column": 4}, "end": {"line": 102, "column": 5}}, {"start": {}, "end": {}}]}, "8": {"loc": {"start": {"line": 119, "column": 4}, "end": {"line": 157, "column": 5}}, "type": "if", "locations": [{"start": {"line": 119, "column": 4}, "end": {"line": 157, "column": 5}}, {"start": {"line": 138, "column": 11}, "end": {"line": 157, "column": 5}}]}, "9": {"loc": {"start": {"line": 124, "column": 6}, "end": {"line": 129, "column": 7}}, "type": "if", "locations": [{"start": {"line": 124, "column": 6}, "end": {"line": 129, "column": 7}}, {"start": {}, "end": {}}]}, "10": {"loc": {"start": {"line": 132, "column": 6}, "end": {"line": 137, "column": 7}}, "type": "if", "locations": [{"start": {"line": 132, "column": 6}, "end": {"line": 137, "column": 7}}, {"start": {}, "end": {}}]}, "11": {"loc": {"start": {"line": 138, "column": 11}, "end": {"line": 157, "column": 5}}, "type": "if", "locations": [{"start": {"line": 138, "column": 11}, "end": {"line": 157, "column": 5}}, {"start": {}, "end": {}}]}, "12": {"loc": {"start": {"line": 143, "column": 6}, "end": {"line": 148, "column": 7}}, "type": "if", "locations": [{"start": {"line": 143, "column": 6}, "end": {"line": 148, "column": 7}}, {"start": {}, "end": {}}]}, "13": {"loc": {"start": {"line": 151, "column": 6}, "end": {"line": 156, "column": 7}}, "type": "if", "locations": [{"start": {"line": 151, "column": 6}, "end": {"line": 156, "column": 7}}, {"start": {}, "end": {}}]}, "14": {"loc": {"start": {"line": 174, "column": 2}, "end": {"line": 191, "column": 3}}, "type": "if", "locations": [{"start": {"line": 174, "column": 2}, "end": {"line": 191, "column": 3}}, {"start": {}, "end": {}}]}, "15": {"loc": {"start": {"line": 185, "column": 12}, "end": {"line": 185, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 185, "column": 33}, "end": {"line": 185, "column": 41}}, {"start": {"line": 185, "column": 44}, "end": {"line": 185, "column": 52}}]}, "16": {"loc": {"start": {"line": 188, "column": 18}, "end": {"line": 188, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 188, "column": 18}, "end": {"line": 188, "column": 27}}, {"start": {"line": 188, "column": 31}, "end": {"line": 188, "column": 33}}]}, "17": {"loc": {"start": {"line": 189, "column": 19}, "end": {"line": 189, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 189, "column": 19}, "end": {"line": 189, "column": 25}}, {"start": {"line": 189, "column": 29}, "end": {"line": 189, "column": 37}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\emprunts\\generateEmpruntLabels.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\emprunts\\generateEmpruntLabels.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 58}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 54}}, "4": {"start": {"line": 6, "column": 11}, "end": {"line": 6, "column": 28}}, "5": {"start": {"line": 23, "column": 13}, "end": {"line": 77, "column": 5}}, "6": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 32}}, "7": {"start": {"line": 26, "column": 4}, "end": {"line": 76, "column": 5}}, "8": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 37}}, "9": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, "10": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 89}}, "11": {"start": {"line": 36, "column": 24}, "end": {"line": 36, "column": 65}}, "12": {"start": {"line": 39, "column": 23}, "end": {"line": 39, "column": 51}}, "13": {"start": {"line": 42, "column": 24}, "end": {"line": 42, "column": 64}}, "14": {"start": {"line": 44, "column": 22}, "end": {"line": 44, "column": 32}}, "15": {"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 42}}, "16": {"start": {"line": 47, "column": 6}, "end": {"line": 51, "column": 9}}, "17": {"start": {"line": 54, "column": 6}, "end": {"line": 56, "column": 7}}, "18": {"start": {"line": 55, "column": 8}, "end": {"line": 55, "column": 92}}, "19": {"start": {"line": 58, "column": 6}, "end": {"line": 63, "column": 8}}, "20": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 73}}, "21": {"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}, "22": {"start": {"line": 69, "column": 8}, "end": {"line": 69, "column": 20}}, "23": {"start": {"line": 72, "column": 6}, "end": {"line": 75, "column": 8}}, "24": {"start": {"line": 83, "column": 21}, "end": {"line": 83, "column": 61}}, "25": {"start": {"line": 84, "column": 21}, "end": {"line": 84, "column": 43}}, "26": {"start": {"line": 86, "column": 2}, "end": {"line": 88, "column": 3}}, "27": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 76}}, "28": {"start": {"line": 90, "column": 22}, "end": {"line": 90, "column": 40}}, "29": {"start": {"line": 93, "column": 27}, "end": {"line": 93, "column": 72}}, "30": {"start": {"line": 94, "column": 28}, "end": {"line": 94, "column": 30}}, "31": {"start": {"line": 96, "column": 2}, "end": {"line": 114, "column": 3}}, "32": {"start": {"line": 97, "column": 25}, "end": {"line": 97, "column": 43}}, "33": {"start": {"line": 99, "column": 4}, "end": {"line": 113, "column": 5}}, "34": {"start": {"line": 100, "column": 24}, "end": {"line": 100, "column": 77}}, "35": {"start": {"line": 101, "column": 24}, "end": {"line": 101, "column": 45}}, "36": {"start": {"line": 103, "column": 6}, "end": {"line": 105, "column": 7}}, "37": {"start": {"line": 104, "column": 8}, "end": {"line": 104, "column": 44}}, "38": {"start": {"line": 106, "column": 11}, "end": {"line": 113, "column": 5}}, "39": {"start": {"line": 107, "column": 23}, "end": {"line": 107, "column": 75}}, "40": {"start": {"line": 108, "column": 23}, "end": {"line": 108, "column": 43}}, "41": {"start": {"line": 110, "column": 6}, "end": {"line": 112, "column": 7}}, "42": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 76}}, "43": {"start": {"line": 116, "column": 2}, "end": {"line": 125, "column": 4}}, "44": {"start": {"line": 133, "column": 17}, "end": {"line": 133, "column": 43}}, "45": {"start": {"line": 136, "column": 15}, "end": {"line": 136, "column": 62}}, "46": {"start": {"line": 137, "column": 19}, "end": {"line": 137, "column": 70}}, "47": {"start": {"line": 140, "column": 21}, "end": {"line": 140, "column": 24}}, "48": {"start": {"line": 141, "column": 22}, "end": {"line": 141, "column": 25}}, "49": {"start": {"line": 144, "column": 15}, "end": {"line": 144, "column": 56}}, "50": {"start": {"line": 147, "column": 21}, "end": {"line": 147, "column": 33}}, "51": {"start": {"line": 148, "column": 20}, "end": {"line": 148, "column": 38}}, "52": {"start": {"line": 151, "column": 18}, "end": {"line": 151, "column": 34}}, "53": {"start": {"line": 152, "column": 21}, "end": {"line": 152, "column": 23}}, "54": {"start": {"line": 153, "column": 21}, "end": {"line": 153, "column": 23}}, "55": {"start": {"line": 156, "column": 2}, "end": {"line": 162, "column": 5}}, "56": {"start": {"line": 164, "column": 2}, "end": {"line": 164, "column": 32}}, "57": {"start": {"line": 167, "column": 2}, "end": {"line": 173, "column": 5}}, "58": {"start": {"line": 175, "column": 2}, "end": {"line": 175, "column": 26}}, "59": {"start": {"line": 178, "column": 19}, "end": {"line": 185, "column": 4}}, "60": {"start": {"line": 187, "column": 2}, "end": {"line": 205, "column": 3}}, "61": {"start": {"line": 188, "column": 4}, "end": {"line": 194, "column": 7}}, "62": {"start": {"line": 196, "column": 4}, "end": {"line": 202, "column": 7}}, "63": {"start": {"line": 204, "column": 4}, "end": {"line": 204, "column": 34}}, "64": {"start": {"line": 207, "column": 2}, "end": {"line": 207, "column": 32}}, "65": {"start": {"line": 210, "column": 2}, "end": {"line": 216, "column": 5}}, "66": {"start": {"line": 218, "column": 2}, "end": {"line": 218, "column": 26}}, "67": {"start": {"line": 221, "column": 2}, "end": {"line": 241, "column": 3}}, "68": {"start": {"line": 222, "column": 4}, "end": {"line": 232, "column": 5}}, "69": {"start": {"line": 223, "column": 6}, "end": {"line": 229, "column": 9}}, "70": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 36}}, "71": {"start": {"line": 234, "column": 4}, "end": {"line": 240, "column": 7}}, "72": {"start": {"line": 244, "column": 2}, "end": {"line": 244, "column": 26}}, "73": {"start": {"line": 245, "column": 2}, "end": {"line": 250, "column": 5}}, "74": {"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 26}}, "75": {"start": {"line": 255, "column": 23}, "end": {"line": 261, "column": 4}}, "76": {"start": {"line": 263, "column": 2}, "end": {"line": 276, "column": 3}}, "77": {"start": {"line": 263, "column": 15}, "end": {"line": 263, "column": 16}}, "78": {"start": {"line": 264, "column": 24}, "end": {"line": 264, "column": 39}}, "79": {"start": {"line": 265, "column": 20}, "end": {"line": 265, "column": 27}}, "80": {"start": {"line": 267, "column": 4}, "end": {"line": 273, "column": 7}}, "81": {"start": {"line": 275, "column": 4}, "end": {"line": 275, "column": 50}}, "82": {"start": {"line": 279, "column": 2}, "end": {"line": 285, "column": 5}}, "83": {"start": {"line": 288, "column": 2}, "end": {"line": 288, "column": 29}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 23, "column": 60}, "end": {"line": 23, "column": 65}}, "loc": {"start": {"line": 23, "column": 94}, "end": {"line": 77, "column": 3}}}, "1": {"name": "getEmpruntLabelData", "decl": {"start": {"line": 82, "column": 15}, "end": {"line": 82, "column": 34}}, "loc": {"start": {"line": 82, "column": 52}, "end": {"line": 126, "column": 1}}}, "2": {"name": "generatePDF", "decl": {"start": {"line": 131, "column": 15}, "end": {"line": 131, "column": 26}}, "loc": {"start": {"line": 131, "column": 47}, "end": {"line": 289, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, "type": "if", "locations": [{"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 31, "column": 10}, "end": {"line": 31, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 31, "column": 10}, "end": {"line": 31, "column": 25}}, {"start": {"line": 31, "column": 29}, "end": {"line": 31, "column": 63}}]}, "2": {"loc": {"start": {"line": 54, "column": 6}, "end": {"line": 56, "column": 7}}, "type": "if", "locations": [{"start": {"line": 54, "column": 6}, "end": {"line": 56, "column": 7}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}, "type": "if", "locations": [{"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 86, "column": 2}, "end": {"line": 88, "column": 3}}, "type": "if", "locations": [{"start": {"line": 86, "column": 2}, "end": {"line": 88, "column": 3}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 99, "column": 4}, "end": {"line": 113, "column": 5}}, "type": "if", "locations": [{"start": {"line": 99, "column": 4}, "end": {"line": 113, "column": 5}}, {"start": {"line": 106, "column": 11}, "end": {"line": 113, "column": 5}}]}, "6": {"loc": {"start": {"line": 103, "column": 6}, "end": {"line": 105, "column": 7}}, "type": "if", "locations": [{"start": {"line": 103, "column": 6}, "end": {"line": 105, "column": 7}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 106, "column": 11}, "end": {"line": 113, "column": 5}}, "type": "if", "locations": [{"start": {"line": 106, "column": 11}, "end": {"line": 113, "column": 5}}, {"start": {}, "end": {}}]}, "8": {"loc": {"start": {"line": 110, "column": 6}, "end": {"line": 112, "column": 7}}, "type": "if", "locations": [{"start": {"line": 110, "column": 6}, "end": {"line": 112, "column": 7}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 221, "column": 2}, "end": {"line": 241, "column": 3}}, "type": "if", "locations": [{"start": {"line": 221, "column": 2}, "end": {"line": 241, "column": 3}}, {"start": {"line": 233, "column": 9}, "end": {"line": 241, "column": 3}}]}, "10": {"loc": {"start": {"line": 270, "column": 12}, "end": {"line": 270, "column": 28}}, "type": "cond-expr", "locations": [{"start": {"line": 270, "column": 22}, "end": {"line": 270, "column": 24}}, {"start": {"line": 270, "column": 27}, "end": {"line": 270, "column": 28}}]}, "11": {"loc": {"start": {"line": 271, "column": 12}, "end": {"line": 271, "column": 37}}, "type": "cond-expr", "locations": [{"start": {"line": 271, "column": 22}, "end": {"line": 271, "column": 30}}, {"start": {"line": 271, "column": 33}, "end": {"line": 271, "column": 37}}]}, "12": {"loc": {"start": {"line": 272, "column": 13}, "end": {"line": 272, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 272, "column": 23}, "end": {"line": 272, "column": 33}}, {"start": {"line": 272, "column": 36}, "end": {"line": 272, "column": 45}}]}, "13": {"loc": {"start": {"line": 275, "column": 31}, "end": {"line": 275, "column": 48}}, "type": "cond-expr", "locations": [{"start": {"line": 275, "column": 41}, "end": {"line": 275, "column": 42}}, {"start": {"line": 275, "column": 45}, "end": {"line": 275, "column": 48}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\emprunts\\updateEmpruntStatus.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\emprunts\\updateEmpruntStatus.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 54}}, "3": {"start": {"line": 5, "column": 11}, "end": {"line": 5, "column": 28}}, "4": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 76}}, "5": {"start": {"line": 9, "column": 56}, "end": {"line": 15, "column": 2}}, "6": {"start": {"line": 28, "column": 13}, "end": {"line": 125, "column": 3}}, "7": {"start": {"line": 29, "column": 2}, "end": {"line": 124, "column": 3}}, "8": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 35}}, "9": {"start": {"line": 34, "column": 26}, "end": {"line": 34, "column": 56}}, "10": {"start": {"line": 37, "column": 19}, "end": {"line": 99, "column": 6}}, "11": {"start": {"line": 38, "column": 25}, "end": {"line": 38, "column": 79}}, "12": {"start": {"line": 39, "column": 25}, "end": {"line": 39, "column": 58}}, "13": {"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 7}}, "14": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 80}}, "15": {"start": {"line": 45, "column": 26}, "end": {"line": 45, "column": 44}}, "16": {"start": {"line": 46, "column": 28}, "end": {"line": 46, "column": 46}}, "17": {"start": {"line": 49, "column": 6}, "end": {"line": 54, "column": 7}}, "18": {"start": {"line": 50, "column": 8}, "end": {"line": 53, "column": 10}}, "19": {"start": {"line": 57, "column": 30}, "end": {"line": 60, "column": 8}}, "20": {"start": {"line": 63, "column": 6}, "end": {"line": 80, "column": 7}}, "21": {"start": {"line": 65, "column": 10}, "end": {"line": 65, "column": 58}}, "22": {"start": {"line": 66, "column": 10}, "end": {"line": 66, "column": 16}}, "23": {"start": {"line": 68, "column": 10}, "end": {"line": 68, "column": 59}}, "24": {"start": {"line": 69, "column": 10}, "end": {"line": 69, "column": 16}}, "25": {"start": {"line": 71, "column": 10}, "end": {"line": 73, "column": 59}}, "26": {"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 60}}, "27": {"start": {"line": 75, "column": 10}, "end": {"line": 75, "column": 16}}, "28": {"start": {"line": 77, "column": 10}, "end": {"line": 77, "column": 42}}, "29": {"start": {"line": 78, "column": 10}, "end": {"line": 78, "column": 64}}, "30": {"start": {"line": 79, "column": 10}, "end": {"line": 79, "column": 16}}, "31": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 49}}, "32": {"start": {"line": 86, "column": 28}, "end": {"line": 86, "column": 69}}, "33": {"start": {"line": 87, "column": 6}, "end": {"line": 92, "column": 9}}, "34": {"start": {"line": 94, "column": 6}, "end": {"line": 98, "column": 8}}, "35": {"start": {"line": 101, "column": 4}, "end": {"line": 106, "column": 7}}, "36": {"start": {"line": 108, "column": 4}, "end": {"line": 111, "column": 6}}, "37": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 78}}, "38": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, "39": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 18}}, "40": {"start": {"line": 120, "column": 4}, "end": {"line": 123, "column": 6}}, "41": {"start": {"line": 131, "column": 2}, "end": {"line": 133, "column": 3}}, "42": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 82}}, "43": {"start": {"line": 135, "column": 2}, "end": {"line": 137, "column": 3}}, "44": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": 85}}, "45": {"start": {"line": 139, "column": 2}, "end": {"line": 144, "column": 3}}, "46": {"start": {"line": 140, "column": 4}, "end": {"line": 143, "column": 6}}, "47": {"start": {"line": 146, "column": 35}, "end": {"line": 150, "column": 4}}, "48": {"start": {"line": 152, "column": 2}, "end": {"line": 158, "column": 3}}, "49": {"start": {"line": 153, "column": 17}, "end": {"line": 153, "column": 51}}, "50": {"start": {"line": 154, "column": 4}, "end": {"line": 156, "column": 5}}, "51": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 90}}, "52": {"start": {"line": 157, "column": 4}, "end": {"line": 157, "column": 38}}, "53": {"start": {"line": 160, "column": 2}, "end": {"line": 160, "column": 16}}, "54": {"start": {"line": 171, "column": 27}, "end": {"line": 171, "column": 83}}, "55": {"start": {"line": 173, "column": 2}, "end": {"line": 187, "column": 3}}, "56": {"start": {"line": 174, "column": 25}, "end": {"line": 174, "column": 43}}, "57": {"start": {"line": 176, "column": 4}, "end": {"line": 186, "column": 5}}, "58": {"start": {"line": 177, "column": 24}, "end": {"line": 177, "column": 77}}, "59": {"start": {"line": 178, "column": 24}, "end": {"line": 178, "column": 56}}, "60": {"start": {"line": 180, "column": 6}, "end": {"line": 185, "column": 7}}, "61": {"start": {"line": 181, "column": 8}, "end": {"line": 184, "column": 10}}, "62": {"start": {"line": 198, "column": 27}, "end": {"line": 198, "column": 83}}, "63": {"start": {"line": 200, "column": 2}, "end": {"line": 210, "column": 3}}, "64": {"start": {"line": 201, "column": 25}, "end": {"line": 201, "column": 43}}, "65": {"start": {"line": 203, "column": 4}, "end": {"line": 209, "column": 5}}, "66": {"start": {"line": 204, "column": 24}, "end": {"line": 204, "column": 77}}, "67": {"start": {"line": 205, "column": 6}, "end": {"line": 208, "column": 9}}, "68": {"start": {"line": 221, "column": 27}, "end": {"line": 221, "column": 83}}, "69": {"start": {"line": 223, "column": 2}, "end": {"line": 261, "column": 3}}, "70": {"start": {"line": 224, "column": 25}, "end": {"line": 224, "column": 43}}, "71": {"start": {"line": 226, "column": 4}, "end": {"line": 255, "column": 5}}, "72": {"start": {"line": 227, "column": 24}, "end": {"line": 227, "column": 77}}, "73": {"start": {"line": 228, "column": 6}, "end": {"line": 231, "column": 9}}, "74": {"start": {"line": 232, "column": 11}, "end": {"line": 255, "column": 5}}, "75": {"start": {"line": 234, "column": 23}, "end": {"line": 234, "column": 75}}, "76": {"start": {"line": 235, "column": 23}, "end": {"line": 235, "column": 54}}, "77": {"start": {"line": 237, "column": 6}, "end": {"line": 254, "column": 7}}, "78": {"start": {"line": 238, "column": 32}, "end": {"line": 238, "column": 57}}, "79": {"start": {"line": 239, "column": 8}, "end": {"line": 242, "column": 11}}, "80": {"start": {"line": 245, "column": 29}, "end": {"line": 245, "column": 68}}, "81": {"start": {"line": 246, "column": 8}, "end": {"line": 253, "column": 11}}, "82": {"start": {"line": 258, "column": 4}, "end": {"line": 260, "column": 7}}, "83": {"start": {"line": 272, "column": 27}, "end": {"line": 272, "column": 83}}, "84": {"start": {"line": 274, "column": 2}, "end": {"line": 278, "column": 3}}, "85": {"start": {"line": 275, "column": 4}, "end": {"line": 277, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 28, "column": 58}, "end": {"line": 28, "column": 63}}, "loc": {"start": {"line": 28, "column": 92}, "end": {"line": 125, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 37, "column": 43}, "end": {"line": 37, "column": 48}}, "loc": {"start": {"line": 37, "column": 65}, "end": {"line": 99, "column": 5}}}, "2": {"name": "validateUpdateStatusData", "decl": {"start": {"line": 130, "column": 9}, "end": {"line": 130, "column": 33}}, "loc": {"start": {"line": 130, "column": 43}, "end": {"line": 161, "column": 1}}}, "3": {"name": "handleStatusPret", "decl": {"start": {"line": 166, "column": 15}, "end": {"line": 166, "column": 31}}, "loc": {"start": {"line": 168, "column": 47}, "end": {"line": 188, "column": 1}}}, "4": {"name": "handleStatusParti", "decl": {"start": {"line": 193, "column": 15}, "end": {"line": 193, "column": 32}}, "loc": {"start": {"line": 195, "column": 47}, "end": {"line": 211, "column": 1}}}, "5": {"name": "handleStatusRevenu", "decl": {"start": {"line": 216, "column": 15}, "end": {"line": 216, "column": 33}}, "loc": {"start": {"line": 218, "column": 47}, "end": {"line": 262, "column": 1}}}, "6": {"name": "handleStatusInventorie", "decl": {"start": {"line": 267, "column": 15}, "end": {"line": 267, "column": 37}}, "loc": {"start": {"line": 269, "column": 47}, "end": {"line": 279, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 7}}, "type": "if", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 7}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 49, "column": 6}, "end": {"line": 54, "column": 7}}, "type": "if", "locations": [{"start": {"line": 49, "column": 6}, "end": {"line": 54, "column": 7}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 49, "column": 11}, "end": {"line": 49, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 49, "column": 44}, "end": {"line": 49, "column": 46}}, {"start": {"line": 49, "column": 44}, "end": {"line": 49, "column": 79}}]}, "3": {"loc": {"start": {"line": 49, "column": 11}, "end": {"line": 49, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 49, "column": 11}, "end": {"line": 49, "column": 46}}, {"start": {"line": 49, "column": 44}, "end": {"line": 49, "column": 46}}]}, "4": {"loc": {"start": {"line": 63, "column": 6}, "end": {"line": 80, "column": 7}}, "type": "switch", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 16}}, {"start": {"line": 67, "column": 8}, "end": {"line": 69, "column": 16}}, {"start": {"line": 70, "column": 8}, "end": {"line": 75, "column": 16}}, {"start": {"line": 76, "column": 8}, "end": {"line": 79, "column": 16}}]}, "5": {"loc": {"start": {"line": 71, "column": 43}, "end": {"line": 73, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 72, "column": 14}, "end": {"line": 72, "column": 83}}, {"start": {"line": 73, "column": 14}, "end": {"line": 73, "column": 58}}]}, "6": {"loc": {"start": {"line": 91, "column": 15}, "end": {"line": 91, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 15}, "end": {"line": 91, "column": 34}}, {"start": {"line": 91, "column": 38}, "end": {"line": 91, "column": 40}}]}, "7": {"loc": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, "type": "if", "locations": [{"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, {"start": {}, "end": {}}]}, "8": {"loc": {"start": {"line": 131, "column": 2}, "end": {"line": 133, "column": 3}}, "type": "if", "locations": [{"start": {"line": 131, "column": 2}, "end": {"line": 133, "column": 3}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 11}}, {"start": {"line": 131, "column": 15}, "end": {"line": 131, "column": 39}}]}, "10": {"loc": {"start": {"line": 135, "column": 2}, "end": {"line": 137, "column": 3}}, "type": "if", "locations": [{"start": {"line": 135, "column": 2}, "end": {"line": 137, "column": 3}}, {"start": {}, "end": {}}]}, "11": {"loc": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 21}}, {"start": {"line": 135, "column": 25}, "end": {"line": 135, "column": 59}}]}, "12": {"loc": {"start": {"line": 139, "column": 2}, "end": {"line": 144, "column": 3}}, "type": "if", "locations": [{"start": {"line": 139, "column": 2}, "end": {"line": 144, "column": 3}}, {"start": {}, "end": {}}]}, "13": {"loc": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 21}}, {"start": {"line": 139, "column": 25}, "end": {"line": 139, "column": 65}}]}, "14": {"loc": {"start": {"line": 149, "column": 11}, "end": {"line": 149, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 11}, "end": {"line": 149, "column": 21}}, {"start": {"line": 149, "column": 25}, "end": {"line": 149, "column": 27}}]}, "15": {"loc": {"start": {"line": 152, "column": 2}, "end": {"line": 158, "column": 3}}, "type": "if", "locations": [{"start": {"line": 152, "column": 2}, "end": {"line": 158, "column": 3}}, {"start": {}, "end": {}}]}, "16": {"loc": {"start": {"line": 154, "column": 4}, "end": {"line": 156, "column": 5}}, "type": "if", "locations": [{"start": {"line": 154, "column": 4}, "end": {"line": 156, "column": 5}}, {"start": {}, "end": {}}]}, "17": {"loc": {"start": {"line": 176, "column": 4}, "end": {"line": 186, "column": 5}}, "type": "if", "locations": [{"start": {"line": 176, "column": 4}, "end": {"line": 186, "column": 5}}, {"start": {}, "end": {}}]}, "18": {"loc": {"start": {"line": 180, "column": 6}, "end": {"line": 185, "column": 7}}, "type": "if", "locations": [{"start": {"line": 180, "column": 6}, "end": {"line": 185, "column": 7}}, {"start": {}, "end": {}}]}, "19": {"loc": {"start": {"line": 180, "column": 10}, "end": {"line": 180, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 180, "column": 10}, "end": {"line": 180, "column": 27}}, {"start": {"line": 180, "column": 31}, "end": {"line": 180, "column": 57}}]}, "20": {"loc": {"start": {"line": 203, "column": 4}, "end": {"line": 209, "column": 5}}, "type": "if", "locations": [{"start": {"line": 203, "column": 4}, "end": {"line": 209, "column": 5}}, {"start": {}, "end": {}}]}, "21": {"loc": {"start": {"line": 226, "column": 4}, "end": {"line": 255, "column": 5}}, "type": "if", "locations": [{"start": {"line": 226, "column": 4}, "end": {"line": 255, "column": 5}}, {"start": {"line": 232, "column": 11}, "end": {"line": 255, "column": 5}}]}, "22": {"loc": {"start": {"line": 232, "column": 11}, "end": {"line": 255, "column": 5}}, "type": "if", "locations": [{"start": {"line": 232, "column": 11}, "end": {"line": 255, "column": 5}}, {"start": {}, "end": {}}]}, "23": {"loc": {"start": {"line": 237, "column": 6}, "end": {"line": 254, "column": 7}}, "type": "if", "locations": [{"start": {"line": 237, "column": 6}, "end": {"line": 254, "column": 7}}, {"start": {}, "end": {}}]}, "24": {"loc": {"start": {"line": 237, "column": 10}, "end": {"line": 237, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 237, "column": 10}, "end": {"line": 237, "column": 25}}, {"start": {"line": 237, "column": 29}, "end": {"line": 237, "column": 61}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0, 0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\auth.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\auth.ts", "statementMap": {"0": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 16}}, "1": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 16}}, "2": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}, "3": {"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 3}}, "4": {"start": {"line": 8, "column": 4}, "end": {"line": 8, "column": 91}}, "5": {"start": {"line": 11, "column": 19}, "end": {"line": 11, "column": 42}}, "6": {"start": {"line": 12, "column": 2}, "end": {"line": 17, "column": 3}}, "7": {"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 6}}, "8": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 49}}}, "fnMap": {"0": {"name": "checkUserRole", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": 29}}, "loc": {"start": {"line": 6, "column": 67}, "end": {"line": 18, "column": 1}}}, "1": {"name": "checkRegisseurOr<PERSON>dmin", "decl": {"start": {"line": 23, "column": 16}, "end": {"line": 23, "column": 37}}, "loc": {"start": {"line": 23, "column": 50}, "end": {"line": 25, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 3}}, "type": "if", "locations": [{"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 3}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 17, "column": 3}}, "type": "if", "locations": [{"start": {"line": 12, "column": 2}, "end": {"line": 17, "column": 3}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 12, "column": 6}, "end": {"line": 12, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 6}, "end": {"line": 12, "column": 15}}, {"start": {"line": 12, "column": 19}, "end": {"line": 12, "column": 52}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\validation.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\validation.ts", "statementMap": {"0": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 16}}, "1": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}, "2": {"start": {"line": 26, "column": 2}, "end": {"line": 28, "column": 3}}, "3": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 82}}, "4": {"start": {"line": 30, "column": 19}, "end": {"line": 30, "column": 105}}, "5": {"start": {"line": 31, "column": 2}, "end": {"line": 35, "column": 3}}, "6": {"start": {"line": 32, "column": 4}, "end": {"line": 34, "column": 5}}, "7": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 110}}, "8": {"start": {"line": 38, "column": 21}, "end": {"line": 38, "column": 46}}, "9": {"start": {"line": 39, "column": 27}, "end": {"line": 39, "column": 58}}, "10": {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, "11": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 80}}, "12": {"start": {"line": 45, "column": 2}, "end": {"line": 47, "column": 3}}, "13": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 124}}, "14": {"start": {"line": 49, "column": 2}, "end": {"line": 59, "column": 4}}}, "fnMap": {"0": {"name": "validateEmpruntData", "decl": {"start": {"line": 25, "column": 16}, "end": {"line": 25, "column": 35}}, "loc": {"start": {"line": 25, "column": 45}, "end": {"line": 60, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 2}, "end": {"line": 28, "column": 3}}, "type": "if", "locations": [{"start": {"line": 26, "column": 2}, "end": {"line": 28, "column": 3}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 11}}, {"start": {"line": 26, "column": 15}, "end": {"line": 26, "column": 39}}]}, "2": {"loc": {"start": {"line": 32, "column": 4}, "end": {"line": 34, "column": 5}}, "type": "if", "locations": [{"start": {"line": 32, "column": 4}, "end": {"line": 34, "column": 5}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 20}}, {"start": {"line": 32, "column": 24}, "end": {"line": 32, "column": 55}}]}, "4": {"loc": {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, "type": "if", "locations": [{"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 33}}, {"start": {"line": 41, "column": 37}, "end": {"line": 41, "column": 70}}]}, "6": {"loc": {"start": {"line": 45, "column": 2}, "end": {"line": 47, "column": 3}}, "type": "if", "locations": [{"start": {"line": 45, "column": 2}, "end": {"line": 47, "column": 3}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 57, "column": 11}, "end": {"line": 57, "column": 46}}, "type": "cond-expr", "locations": [{"start": {"line": 57, "column": 24}, "end": {"line": 57, "column": 41}}, {"start": {"line": 57, "column": 44}, "end": {"line": 57, "column": 46}}]}, "8": {"loc": {"start": {"line": 58, "column": 14}, "end": {"line": 58, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 14}, "end": {"line": 58, "column": 27}}, {"start": {"line": 58, "column": 31}, "end": {"line": 58, "column": 33}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}}