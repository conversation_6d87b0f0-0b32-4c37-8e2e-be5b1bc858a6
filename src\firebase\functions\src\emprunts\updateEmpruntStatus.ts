import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { checkRegisseurOrAdmin } from '../utils/auth';

const db = admin.firestore();

// Statuts valides et leurs transitions autorisées
const VALID_STATUSES = ['Pas prêt', 'Prêt', 'Parti', 'Revenu', 'Inventorié'];
const STATUS_TRANSITIONS: { [key: string]: string[] } = {
  'Pas prêt': ['Prêt'],
  'Prêt': ['Parti', 'Pas prêt'],
  'Parti': ['Revenu'],
  'Revenu': ['Inventorié', 'Parti'],
  'Inventorié': []
};

interface UpdateStatusData {
  empruntId: string;
  newStatus: string;
  notes?: string;
  dateRetourEffective?: Date;
}

/**
 * Cloud Function pour mettre à jour le statut d'un emprunt
 * Gère les transitions de statut et les mises à jour associées
 */
export const updateEmpruntStatus = functions.https.onCall(async (data: any, context: any) => {
  try {
    // Vérification des permissions
    checkRegisseurOrAdmin(context);

    // Validation des données
    const validatedData = validateUpdateStatusData(data);

    // Mise à jour avec transaction
    const result = await db.runTransaction(async (transaction) => {
      const empruntRef = db.collection('emprunts').doc(validatedData.empruntId);
      const empruntDoc = await transaction.get(empruntRef);

      if (!empruntDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Emprunt non trouvé');
      }

      const empruntData = empruntDoc.data()!;
      const currentStatus = empruntData.statut;

      // Vérifier que la transition est autorisée
      if (!STATUS_TRANSITIONS[currentStatus]?.includes(validatedData.newStatus)) {
        throw new functions.https.HttpsError(
          'failed-precondition',
          `Transition non autorisée de "${currentStatus}" vers "${validatedData.newStatus}"`
        );
      }

      // Préparer les données de mise à jour
      const updateData: any = {
        statut: validatedData.newStatus,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      // Gestion spécifique selon le nouveau statut
      switch (validatedData.newStatus) {
        case 'Prêt':
          await handleStatusPret(transaction, empruntRef);
          break;
        case 'Parti':
          await handleStatusParti(transaction, empruntRef);
          break;
        case 'Revenu':
          updateData.dateRetourEffective = validatedData.dateRetourEffective 
            ? admin.firestore.Timestamp.fromDate(validatedData.dateRetourEffective)
            : admin.firestore.FieldValue.serverTimestamp();
          await handleStatusRevenu(transaction, empruntRef);
          break;
        case 'Inventorié':
          updateData.estInventorie = true;
          await handleStatusInventorie(transaction, empruntRef);
          break;
      }

      // Mise à jour du document principal
      transaction.update(empruntRef, updateData);

      // Ajout dans l'historique
      const historiqueRef = empruntRef.collection('historique').doc();
      transaction.set(historiqueRef, {
        date: admin.firestore.FieldValue.serverTimestamp(),
        action: `Changement de statut: ${currentStatus} → ${validatedData.newStatus}`,
        utilisateur: context.auth!.uid,
        notes: validatedData.notes || ''
      });

      return {
        id: validatedData.empruntId,
        oldStatus: currentStatus,
        newStatus: validatedData.newStatus
      };
    });

    functions.logger.info(`Statut d'emprunt mis à jour: ${result.id}`, {
      empruntId: result.id,
      oldStatus: result.oldStatus,
      newStatus: result.newStatus,
      updatedBy: context.auth!.uid
    });

    return {
      success: true,
      result
    };

  } catch (error) {
    functions.logger.error('Erreur lors de la mise à jour du statut:', error);
    
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    
    throw new functions.https.HttpsError(
      'internal',
      'Erreur interne lors de la mise à jour du statut'
    );
  }
});

/**
 * Valide les données de mise à jour de statut
 */
function validateUpdateStatusData(data: any): UpdateStatusData {
  if (!data || typeof data !== 'object') {
    throw new functions.https.HttpsError('invalid-argument', 'Données invalides');
  }

  if (!data.empruntId || typeof data.empruntId !== 'string') {
    throw new functions.https.HttpsError('invalid-argument', 'ID d\'emprunt requis');
  }

  if (!data.newStatus || !VALID_STATUSES.includes(data.newStatus)) {
    throw new functions.https.HttpsError(
      'invalid-argument', 
      `Statut invalide. Statuts valides: ${VALID_STATUSES.join(', ')}`
    );
  }

  const result: UpdateStatusData = {
    empruntId: data.empruntId,
    newStatus: data.newStatus,
    notes: data.notes || ''
  };

  if (data.dateRetourEffective) {
    const date = new Date(data.dateRetourEffective);
    if (isNaN(date.getTime())) {
      throw new functions.https.HttpsError('invalid-argument', 'Date de retour invalide');
    }
    result.dateRetourEffective = date;
  }

  return result;
}

/**
 * Gestion du passage au statut "Prêt"
 */
async function handleStatusPret(
  transaction: admin.firestore.Transaction,
  empruntRef: admin.firestore.DocumentReference
): Promise<void> {
  // Vérifier que tout le matériel est disponible et prêt
  const materielSnapshot = await transaction.get(empruntRef.collection('materiel'));
  
  for (const materielDoc of materielSnapshot.docs) {
    const materielData = materielDoc.data();
    
    if (materielData.type === 'module') {
      const moduleRef = db.collection('modules').doc(materielData.idMateriel);
      const moduleDoc = await transaction.get(moduleRef);
      
      if (!moduleDoc.exists || !moduleDoc.data()!.estPret) {
        throw new functions.https.HttpsError(
          'failed-precondition',
          `Module non prêt: ${materielData.idMateriel}`
        );
      }
    }
  }
}

/**
 * Gestion du passage au statut "Parti"
 */
async function handleStatusParti(
  transaction: admin.firestore.Transaction,
  empruntRef: admin.firestore.DocumentReference
): Promise<void> {
  // Marquer les modules comme non disponibles
  const materielSnapshot = await transaction.get(empruntRef.collection('materiel'));
  
  for (const materielDoc of materielSnapshot.docs) {
    const materielData = materielDoc.data();
    
    if (materielData.type === 'module') {
      const moduleRef = db.collection('modules').doc(materielData.idMateriel);
      transaction.update(moduleRef, {
        estPret: false,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  }
}

/**
 * Gestion du passage au statut "Revenu"
 */
async function handleStatusRevenu(
  transaction: admin.firestore.Transaction,
  empruntRef: admin.firestore.DocumentReference
): Promise<void> {
  // Remettre les modules comme disponibles
  const materielSnapshot = await transaction.get(empruntRef.collection('materiel'));
  
  for (const materielDoc of materielSnapshot.docs) {
    const materielData = materielDoc.data();
    
    if (materielData.type === 'module') {
      const moduleRef = db.collection('modules').doc(materielData.idMateriel);
      transaction.update(moduleRef, {
        estPret: true,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    } else if (materielData.type === 'stock') {
      // Remettre en stock (si pas consommable)
      const stockRef = db.collection('stocks').doc(materielData.idMateriel);
      const stockDoc = await transaction.get(stockRef);
      
      if (stockDoc.exists && !stockDoc.data()!.estConsommable) {
        const currentQuantity = stockDoc.data()!.quantite;
        transaction.update(stockRef, {
          quantite: currentQuantity + materielData.quantite,
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Ajouter un mouvement de stock
        const mouvementRef = stockRef.collection('mouvements').doc();
        transaction.set(mouvementRef, {
          date: admin.firestore.FieldValue.serverTimestamp(),
          type: 'entree',
          quantite: materielData.quantite,
          motif: 'Retour d\'emprunt',
          refEmprunt: empruntRef.id,
          utilisateur: 'system'
        });
      }
    }

    // Marquer le matériel comme retourné
    transaction.update(materielDoc.ref, {
      estRetourne: true
    });
  }
}

/**
 * Gestion du passage au statut "Inventorié"
 */
async function handleStatusInventorie(
  transaction: admin.firestore.Transaction,
  empruntRef: admin.firestore.DocumentReference
): Promise<void> {
  // Marquer tout le matériel comme complet (peut être modifié manuellement si nécessaire)
  const materielSnapshot = await transaction.get(empruntRef.collection('materiel'));
  
  for (const materielDoc of materielSnapshot.docs) {
    transaction.update(materielDoc.ref, {
      estComplet: true
    });
  }
}
