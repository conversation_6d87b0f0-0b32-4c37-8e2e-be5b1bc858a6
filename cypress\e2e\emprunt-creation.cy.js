describe('Création d\'emprunt - Formulaire multi-étapes', () => {
  beforeEach(() => {
    // Visite de la page de création d'emprunt
    cy.visit('/emprunts/nouveau')
    
    // Mock de l'authentification
    cy.window().then((win) => {
      win.localStorage.setItem('user', JSON.stringify({
        uid: 'test-user',
        role: 'regisseur',
        email: '<EMAIL>'
      }))
    })
  })

  it('devrait afficher le formulaire de création d\'emprunt', () => {
    cy.get('[data-cy=emprunt-form]').should('be.visible')
    cy.get('[data-cy=step-indicator]').should('contain', 'Étape 1 sur 4')
  })

  it('devrait naviguer à travers toutes les étapes du wizard', () => {
    // Étape 1: Informations de base
    cy.get('[data-cy=nom-manipulation]').type('Test Manipulation Cypress')
    cy.get('[data-cy=lieu]').type('Salle de test')
    cy.get('[data-cy=date-depart]').type('2024-12-01')
    cy.get('[data-cy=date-retour]').type('2024-12-05')
    cy.get('[data-cy=secteur]').select('Test Secteur')
    cy.get('[data-cy=referent]').type('Test Référent')
    cy.get('[data-cy=emprunteur]').type('Test Emprunteur')
    
    cy.get('[data-cy=next-step]').click()

    // Étape 2: Sélection du matériel
    cy.get('[data-cy=step-indicator]').should('contain', 'Étape 2 sur 4')
    cy.get('[data-cy=materiel-search]').type('module test')
    cy.get('[data-cy=materiel-item]').first().click()
    cy.get('[data-cy=add-materiel]').click()
    
    cy.get('[data-cy=next-step]').click()

    // Étape 3: Livraison (optionnel)
    cy.get('[data-cy=step-indicator]').should('contain', 'Étape 3 sur 4')
    cy.get('[data-cy=skip-livraison]').click()

    // Étape 4: Validation
    cy.get('[data-cy=step-indicator]').should('contain', 'Étape 4 sur 4')
    cy.get('[data-cy=emprunt-summary]').should('be.visible')
    cy.get('[data-cy=summary-nom]').should('contain', 'Test Manipulation Cypress')
    cy.get('[data-cy=summary-lieu]').should('contain', 'Salle de test')
  })

  it('devrait valider les champs requis', () => {
    // Essayer de passer à l'étape suivante sans remplir les champs
    cy.get('[data-cy=next-step]').click()
    
    // Vérifier que les erreurs sont affichées
    cy.get('[data-cy=error-nom]').should('be.visible')
    cy.get('[data-cy=error-lieu]').should('be.visible')
    cy.get('[data-cy=error-date-depart]').should('be.visible')
    cy.get('[data-cy=error-date-retour]').should('be.visible')
  })

  it('devrait valider les dates', () => {
    cy.get('[data-cy=nom-manipulation]').type('Test Manipulation')
    cy.get('[data-cy=lieu]').type('Salle de test')
    cy.get('[data-cy=date-depart]').type('2024-12-05')
    cy.get('[data-cy=date-retour]').type('2024-12-01') // Date antérieure
    cy.get('[data-cy=secteur]').select('Test Secteur')
    cy.get('[data-cy=referent]').type('Test Référent')
    cy.get('[data-cy=emprunteur]').type('Test Emprunteur')
    
    cy.get('[data-cy=next-step]').click()
    
    // Vérifier l'erreur de validation des dates
    cy.get('[data-cy=error-dates]').should('contain', 'La date de retour doit être postérieure')
  })

  it('devrait permettre de revenir aux étapes précédentes', () => {
    // Aller à l'étape 2
    cy.get('[data-cy=nom-manipulation]').type('Test Manipulation')
    cy.get('[data-cy=lieu]').type('Salle de test')
    cy.get('[data-cy=date-depart]').type('2024-12-01')
    cy.get('[data-cy=date-retour]').type('2024-12-05')
    cy.get('[data-cy=secteur]').select('Test Secteur')
    cy.get('[data-cy=referent]').type('Test Référent')
    cy.get('[data-cy=emprunteur]').type('Test Emprunteur')
    cy.get('[data-cy=next-step]').click()

    // Revenir à l'étape 1
    cy.get('[data-cy=prev-step]').click()
    cy.get('[data-cy=step-indicator]').should('contain', 'Étape 1 sur 4')
    
    // Vérifier que les données sont conservées
    cy.get('[data-cy=nom-manipulation]').should('have.value', 'Test Manipulation')
  })

  it('devrait créer l\'emprunt avec succès', () => {
    // Remplir le formulaire complet
    cy.get('[data-cy=nom-manipulation]').type('Test Manipulation Complète')
    cy.get('[data-cy=lieu]').type('Salle de test complète')
    cy.get('[data-cy=date-depart]').type('2024-12-01')
    cy.get('[data-cy=date-retour]').type('2024-12-05')
    cy.get('[data-cy=secteur]').select('Test Secteur')
    cy.get('[data-cy=referent]').type('Test Référent')
    cy.get('[data-cy=emprunteur]').type('Test Emprunteur')
    cy.get('[data-cy=next-step]').click()

    // Étape 2: Ajouter du matériel
    cy.get('[data-cy=materiel-search]').type('module test')
    cy.get('[data-cy=materiel-item]').first().click()
    cy.get('[data-cy=add-materiel]').click()
    cy.get('[data-cy=next-step]').click()

    // Étape 3: Passer la livraison
    cy.get('[data-cy=skip-livraison]').click()

    // Étape 4: Valider la création
    cy.get('[data-cy=create-emprunt]').click()

    // Vérifier le succès
    cy.get('[data-cy=success-message]').should('be.visible')
    cy.get('[data-cy=success-message]').should('contain', 'Emprunt créé avec succès')
    
    // Vérifier la redirection
    cy.url().should('include', '/emprunts/')
  })

  it('devrait afficher les erreurs de création', () => {
    // Mock d'une erreur de création
    cy.intercept('POST', '**/createEmprunt', {
      statusCode: 400,
      body: { error: 'Erreur de création' }
    }).as('createEmpruntError')

    // Remplir et soumettre le formulaire
    cy.get('[data-cy=nom-manipulation]').type('Test Erreur')
    cy.get('[data-cy=lieu]').type('Salle de test')
    cy.get('[data-cy=date-depart]').type('2024-12-01')
    cy.get('[data-cy=date-retour]').type('2024-12-05')
    cy.get('[data-cy=secteur]').select('Test Secteur')
    cy.get('[data-cy=referent]').type('Test Référent')
    cy.get('[data-cy=emprunteur]').type('Test Emprunteur')
    cy.get('[data-cy=next-step]').click()
    cy.get('[data-cy=skip-livraison]').click()
    cy.get('[data-cy=skip-livraison]').click()
    cy.get('[data-cy=create-emprunt]').click()

    // Vérifier l'affichage de l'erreur
    cy.wait('@createEmpruntError')
    cy.get('[data-cy=error-message]').should('be.visible')
    cy.get('[data-cy=error-message]').should('contain', 'Erreur de création')
  })

  it('devrait sauvegarder automatiquement le brouillon', () => {
    // Remplir partiellement le formulaire
    cy.get('[data-cy=nom-manipulation]').type('Brouillon Test')
    cy.get('[data-cy=lieu]').type('Salle brouillon')
    
    // Attendre la sauvegarde automatique
    cy.wait(2000)
    
    // Vérifier l'indicateur de sauvegarde
    cy.get('[data-cy=draft-saved]').should('be.visible')
    
    // Recharger la page
    cy.reload()
    
    // Vérifier que les données sont restaurées
    cy.get('[data-cy=nom-manipulation]').should('have.value', 'Brouillon Test')
    cy.get('[data-cy=lieu]').should('have.value', 'Salle brouillon')
  })
})
