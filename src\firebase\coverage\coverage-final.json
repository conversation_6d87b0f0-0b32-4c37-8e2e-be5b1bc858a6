{"C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\index.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 14, "column": 0}, "end": {"line": 16, "column": 1}}, "2": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 24}}, "3": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 36}}, "4": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 35}}, "5": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 38}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 0}, "end": {"line": 16, "column": 1}}, "type": "if", "locations": [{"start": {"line": 14, "column": 0}, "end": {"line": 16, "column": 1}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {}, "b": {"0": [0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\auth\\onUserCreate.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\auth\\onUserCreate.ts", "statementMap": {"0": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 51}}, "1": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 58}}, "2": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 56}}, "3": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 52}}, "4": {"start": {"line": 24, "column": 13}, "end": {"line": 161, "column": 5}}, "5": {"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 32}}, "6": {"start": {"line": 28, "column": 4}, "end": {"line": 160, "column": 5}}, "7": {"start": {"line": 29, "column": 6}, "end": {"line": 34, "column": 9}}, "8": {"start": {"line": 37, "column": 19}, "end": {"line": 37, "column": 28}}, "9": {"start": {"line": 38, "column": 26}, "end": {"line": 38, "column": 39}}, "10": {"start": {"line": 39, "column": 27}, "end": {"line": 44, "column": 8}}, "11": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 61}}, "12": {"start": {"line": 48, "column": 6}, "end": {"line": 51, "column": 9}}, "13": {"start": {"line": 54, "column": 17}, "end": {"line": 54, "column": 31}}, "14": {"start": {"line": 57, "column": 6}, "end": {"line": 129, "column": 9}}, "15": {"start": {"line": 58, "column": 24}, "end": {"line": 58, "column": 60}}, "16": {"start": {"line": 61, "column": 28}, "end": {"line": 61, "column": 58}}, "17": {"start": {"line": 62, "column": 8}, "end": {"line": 68, "column": 9}}, "18": {"start": {"line": 63, "column": 10}, "end": {"line": 66, "column": 13}}, "19": {"start": {"line": 67, "column": 10}, "end": {"line": 67, "column": 17}}, "20": {"start": {"line": 71, "column": 25}, "end": {"line": 120, "column": 10}}, "21": {"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 43}}, "22": {"start": {"line": 124, "column": 8}, "end": {"line": 128, "column": 11}}, "23": {"start": {"line": 132, "column": 28}, "end": {"line": 132, "column": 50}}, "24": {"start": {"line": 133, "column": 6}, "end": {"line": 139, "column": 9}}, "25": {"start": {"line": 145, "column": 28}, "end": {"line": 145, "column": 50}}, "26": {"start": {"line": 147, "column": 6}, "end": {"line": 153, "column": 9}}}, "fnMap": {"0": {"name": "(anonymous_9)", "decl": {"start": {"line": 25, "column": 24}, "end": {"line": 25, "column": 29}}, "loc": {"start": {"line": 25, "column": 51}, "end": {"line": 161, "column": 3}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 57, "column": 30}, "end": {"line": 57, "column": 35}}, "loc": {"start": {"line": 57, "column": 52}, "end": {"line": 129, "column": 7}}}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 18}, "end": {"line": 33, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 33, "column": 40}, "end": {"line": 33, "column": 42}}, {"start": {"line": 33, "column": 40}, "end": {"line": 33, "column": 52}}]}, "1": {"loc": {"start": {"line": 33, "column": 18}, "end": {"line": 33, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 18}, "end": {"line": 33, "column": 42}}, {"start": {"line": 33, "column": 40}, "end": {"line": 33, "column": 42}}]}, "2": {"loc": {"start": {"line": 33, "column": 18}, "end": {"line": 33, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 33, "column": 35}, "end": {"line": 33, "column": 38}}, {"start": {"line": 33, "column": 35}, "end": {"line": 33, "column": 40}}]}, "3": {"loc": {"start": {"line": 33, "column": 18}, "end": {"line": 33, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 18}, "end": {"line": 33, "column": 38}}, {"start": {"line": 33, "column": 35}, "end": {"line": 33, "column": 38}}]}, "4": {"loc": {"start": {"line": 62, "column": 8}, "end": {"line": 68, "column": 9}}, "type": "if", "locations": [{"start": {"line": 62, "column": 8}, "end": {"line": 68, "column": 9}}]}, "5": {"loc": {"start": {"line": 73, "column": 17}, "end": {"line": 73, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 73, "column": 17}, "end": {"line": 73, "column": 27}}, {"start": {"line": 73, "column": 31}, "end": {"line": 73, "column": 33}}]}, "6": {"loc": {"start": {"line": 74, "column": 23}, "end": {"line": 74, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 23}, "end": {"line": 74, "column": 39}}, {"start": {"line": 74, "column": 43}, "end": {"line": 74, "column": 45}}]}, "7": {"loc": {"start": {"line": 75, "column": 20}, "end": {"line": 75, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 75, "column": 20}, "end": {"line": 75, "column": 33}}, {"start": {"line": 75, "column": 37}, "end": {"line": 75, "column": 39}}]}, "8": {"loc": {"start": {"line": 97, "column": 20}, "end": {"line": 97, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 97, "column": 20}, "end": {"line": 97, "column": 54}}, {"start": {"line": 97, "column": 58}, "end": {"line": 97, "column": 67}}]}, "9": {"loc": {"start": {"line": 97, "column": 20}, "end": {"line": 97, "column": 54}}, "type": "cond-expr", "locations": [{"start": {"line": 97, "column": 42}, "end": {"line": 97, "column": 44}}, {"start": {"line": 97, "column": 42}, "end": {"line": 97, "column": 54}}]}, "10": {"loc": {"start": {"line": 97, "column": 20}, "end": {"line": 97, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 97, "column": 20}, "end": {"line": 97, "column": 44}}, {"start": {"line": 97, "column": 42}, "end": {"line": 97, "column": 44}}]}, "11": {"loc": {"start": {"line": 97, "column": 20}, "end": {"line": 97, "column": 42}}, "type": "cond-expr", "locations": [{"start": {"line": 97, "column": 37}, "end": {"line": 97, "column": 40}}, {"start": {"line": 97, "column": 37}, "end": {"line": 97, "column": 42}}]}, "12": {"loc": {"start": {"line": 97, "column": 20}, "end": {"line": 97, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 97, "column": 20}, "end": {"line": 97, "column": 40}}, {"start": {"line": 97, "column": 37}, "end": {"line": 97, "column": 40}}]}, "13": {"loc": {"start": {"line": 101, "column": 25}, "end": {"line": 101, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 101, "column": 25}, "end": {"line": 101, "column": 43}}, {"start": {"line": 101, "column": 47}, "end": {"line": 101, "column": 52}}]}, "14": {"loc": {"start": {"line": 138, "column": 18}, "end": {"line": 138, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 138, "column": 40}, "end": {"line": 138, "column": 42}}, {"start": {"line": 138, "column": 40}, "end": {"line": 138, "column": 52}}]}, "15": {"loc": {"start": {"line": 138, "column": 18}, "end": {"line": 138, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 138, "column": 18}, "end": {"line": 138, "column": 42}}, {"start": {"line": 138, "column": 40}, "end": {"line": 138, "column": 42}}]}, "16": {"loc": {"start": {"line": 138, "column": 18}, "end": {"line": 138, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 138, "column": 35}, "end": {"line": 138, "column": 38}}, {"start": {"line": 138, "column": 35}, "end": {"line": 138, "column": 40}}]}, "17": {"loc": {"start": {"line": 138, "column": 18}, "end": {"line": 138, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 138, "column": 18}, "end": {"line": 138, "column": 38}}, {"start": {"line": 138, "column": 35}, "end": {"line": 138, "column": 38}}]}, "18": {"loc": {"start": {"line": 150, "column": 15}, "end": {"line": 150, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 150, "column": 40}, "end": {"line": 150, "column": 53}}, {"start": {"line": 150, "column": 56}, "end": {"line": 150, "column": 69}}]}, "19": {"loc": {"start": {"line": 151, "column": 15}, "end": {"line": 151, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 151, "column": 40}, "end": {"line": 151, "column": 51}}, {"start": {"line": 151, "column": 54}, "end": {"line": 151, "column": 63}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\auth\\setUserRole.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\auth\\setUserRole.ts", "statementMap": {"0": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 16}}, "1": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 16}}, "2": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 65}}, "3": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 46}}, "4": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 56}}, "5": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 52}}, "6": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 24}}, "7": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 91}}, "8": {"start": {"line": 14, "column": 26}, "end": {"line": 20, "column": 2}}, "9": {"start": {"line": 36, "column": 13}, "end": {"line": 201, "column": 2}}, "10": {"start": {"line": 44, "column": 22}, "end": {"line": 44, "column": 32}}, "11": {"start": {"line": 46, "column": 4}, "end": {"line": 199, "column": 5}}, "12": {"start": {"line": 48, "column": 21}, "end": {"line": 48, "column": 50}}, "13": {"start": {"line": 49, "column": 23}, "end": {"line": 49, "column": 33}}, "14": {"start": {"line": 50, "column": 26}, "end": {"line": 50, "column": 38}}, "15": {"start": {"line": 52, "column": 6}, "end": {"line": 57, "column": 9}}, "16": {"start": {"line": 60, "column": 28}, "end": {"line": 63, "column": null}}, "17": {"start": {"line": 62, "column": 18}, "end": {"line": 62, "column": 47}}, "18": {"start": {"line": 66, "column": 39}, "end": {"line": 66, "column": 52}}, "19": {"start": {"line": 69, "column": 19}, "end": {"line": 69, "column": 28}}, "20": {"start": {"line": 71, "column": 6}, "end": {"line": 76, "column": 7}}, "21": {"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 48}}, "22": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 89}}, "23": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 69}}, "24": {"start": {"line": 79, "column": 28}, "end": {"line": 79, "column": 57}}, "25": {"start": {"line": 80, "column": 26}, "end": {"line": 80, "column": 61}}, "26": {"start": {"line": 83, "column": 6}, "end": {"line": 91, "column": 7}}, "27": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 72}}, "28": {"start": {"line": 85, "column": 8}, "end": {"line": 90, "column": 10}}, "29": {"start": {"line": 94, "column": 17}, "end": {"line": 94, "column": 31}}, "30": {"start": {"line": 95, "column": 21}, "end": {"line": 143, "column": 8}}, "31": {"start": {"line": 97, "column": 8}, "end": {"line": 102, "column": 11}}, "32": {"start": {"line": 105, "column": 24}, "end": {"line": 105, "column": 58}}, "33": {"start": {"line": 106, "column": 24}, "end": {"line": 106, "column": 54}}, "34": {"start": {"line": 108, "column": 25}, "end": {"line": 126, "column": 10}}, "35": {"start": {"line": 128, "column": 8}, "end": {"line": 136, "column": 9}}, "36": {"start": {"line": 129, "column": 10}, "end": {"line": 129, "column": 48}}, "37": {"start": {"line": 131, "column": 10}, "end": {"line": 135, "column": 13}}, "38": {"start": {"line": 138, "column": 8}, "end": {"line": 142, "column": 10}}, "39": {"start": {"line": 146, "column": 28}, "end": {"line": 146, "column": 50}}, "40": {"start": {"line": 147, "column": 6}, "end": {"line": 156, "column": 9}}, "41": {"start": {"line": 159, "column": 6}, "end": {"line": 166, "column": 9}}, "42": {"start": {"line": 168, "column": 6}, "end": {"line": 174, "column": 8}}, "43": {"start": {"line": 177, "column": 28}, "end": {"line": 177, "column": 50}}, "44": {"start": {"line": 180, "column": 6}, "end": {"line": 188, "column": 7}}, "45": {"start": {"line": 181, "column": 8}, "end": {"line": 186, "column": 11}}, "46": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 20}}, "47": {"start": {"line": 191, "column": 6}, "end": {"line": 196, "column": 9}}, "48": {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 68}}, "49": {"start": {"line": 207, "column": 2}, "end": {"line": 207, "column": 62}}, "50": {"start": {"line": 215, "column": 2}, "end": {"line": 217, "column": 3}}, "51": {"start": {"line": 216, "column": 4}, "end": {"line": 216, "column": 17}}, "52": {"start": {"line": 220, "column": 2}, "end": {"line": 220, "column": 33}}}, "fnMap": {"0": {"name": "(anonymous_9)", "decl": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 7}}, "loc": {"start": {"line": 43, "column": 20}, "end": {"line": 200, "column": 3}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 9}}, "loc": {"start": {"line": 62, "column": 18}, "end": {"line": 62, "column": 47}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 95, "column": 45}, "end": {"line": 95, "column": 50}}, "loc": {"start": {"line": 95, "column": 67}, "end": {"line": 143, "column": 7}}}, "3": {"name": "isValidRole", "decl": {"start": {"line": 206, "column": 16}, "end": {"line": 206, "column": 27}}, "loc": {"start": {"line": 206, "column": 40}, "end": {"line": 208, "column": 1}}}, "4": {"name": "canAssignRole", "decl": {"start": {"line": 213, "column": 16}, "end": {"line": 213, "column": 29}}, "loc": {"start": {"line": 213, "column": 66}, "end": {"line": 221, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 79, "column": 28}, "end": {"line": 79, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 79, "column": 28}, "end": {"line": 79, "column": 51}}, {"start": {"line": 79, "column": 55}, "end": {"line": 79, "column": 57}}]}, "1": {"loc": {"start": {"line": 80, "column": 26}, "end": {"line": 80, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 80, "column": 26}, "end": {"line": 80, "column": 44}}, {"start": {"line": 80, "column": 48}, "end": {"line": 80, "column": 61}}]}, "2": {"loc": {"start": {"line": 83, "column": 6}, "end": {"line": 91, "column": 7}}, "type": "if", "locations": [{"start": {"line": 83, "column": 6}, "end": {"line": 91, "column": 7}}]}, "3": {"loc": {"start": {"line": 111, "column": 23}, "end": {"line": 111, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 111, "column": 23}, "end": {"line": 111, "column": 45}}, {"start": {"line": 111, "column": 49}, "end": {"line": 111, "column": 51}}]}, "4": {"loc": {"start": {"line": 114, "column": 16}, "end": {"line": 114, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 114, "column": 33}, "end": {"line": 114, "column": 66}}, {"start": {"line": 114, "column": 69}, "end": {"line": 114, "column": 71}}]}, "5": {"loc": {"start": {"line": 114, "column": 33}, "end": {"line": 114, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 33}, "end": {"line": 114, "column": 60}}, {"start": {"line": 114, "column": 64}, "end": {"line": 114, "column": 66}}]}, "6": {"loc": {"start": {"line": 114, "column": 33}, "end": {"line": 114, "column": 60}}, "type": "cond-expr", "locations": [{"start": {"line": 114, "column": 47}, "end": {"line": 114, "column": 49}}, {"start": {"line": 114, "column": 47}, "end": {"line": 114, "column": 60}}]}, "7": {"loc": {"start": {"line": 114, "column": 33}, "end": {"line": 114, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 33}, "end": {"line": 114, "column": 49}}, {"start": {"line": 114, "column": 47}, "end": {"line": 114, "column": 49}}]}, "8": {"loc": {"start": {"line": 121, "column": 22}, "end": {"line": 121, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 121, "column": 22}, "end": {"line": 121, "column": 28}}, {"start": {"line": 121, "column": 32}, "end": {"line": 121, "column": 47}}]}, "9": {"loc": {"start": {"line": 128, "column": 8}, "end": {"line": 136, "column": 9}}, "type": "if", "locations": [{"start": {"line": 128, "column": 8}, "end": {"line": 136, "column": 9}}, {"start": {"line": 130, "column": 15}, "end": {"line": 136, "column": 9}}]}, "10": {"loc": {"start": {"line": 154, "column": 16}, "end": {"line": 154, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 154, "column": 16}, "end": {"line": 154, "column": 22}}, {"start": {"line": 154, "column": 26}, "end": {"line": 154, "column": 41}}]}, "11": {"loc": {"start": {"line": 164, "column": 16}, "end": {"line": 164, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 164, "column": 16}, "end": {"line": 164, "column": 22}}, {"start": {"line": 164, "column": 26}, "end": {"line": 164, "column": 41}}]}, "12": {"loc": {"start": {"line": 180, "column": 6}, "end": {"line": 188, "column": 7}}, "type": "if", "locations": [{"start": {"line": 180, "column": 6}, "end": {"line": 188, "column": 7}}]}, "13": {"loc": {"start": {"line": 184, "column": 20}, "end": {"line": 184, "column": 37}}, "type": "cond-expr", "locations": [{"start": {"line": 184, "column": 32}, "end": {"line": 184, "column": 34}}, {"start": {"line": 184, "column": 32}, "end": {"line": 184, "column": 37}}]}, "14": {"loc": {"start": {"line": 184, "column": 20}, "end": {"line": 184, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 184, "column": 20}, "end": {"line": 184, "column": 34}}, {"start": {"line": 184, "column": 32}, "end": {"line": 184, "column": 34}}]}, "15": {"loc": {"start": {"line": 192, "column": 15}, "end": {"line": 192, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 192, "column": 40}, "end": {"line": 192, "column": 53}}, {"start": {"line": 192, "column": 56}, "end": {"line": 192, "column": 69}}]}, "16": {"loc": {"start": {"line": 193, "column": 15}, "end": {"line": 193, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 193, "column": 40}, "end": {"line": 193, "column": 51}}, {"start": {"line": 193, "column": 54}, "end": {"line": 193, "column": 63}}]}, "17": {"loc": {"start": {"line": 194, "column": 18}, "end": {"line": 194, "column": 35}}, "type": "cond-expr", "locations": [{"start": {"line": 194, "column": 30}, "end": {"line": 194, "column": 32}}, {"start": {"line": 194, "column": 30}, "end": {"line": 194, "column": 35}}]}, "18": {"loc": {"start": {"line": 194, "column": 18}, "end": {"line": 194, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 194, "column": 18}, "end": {"line": 194, "column": 32}}, {"start": {"line": 194, "column": 30}, "end": {"line": 194, "column": 32}}]}, "19": {"loc": {"start": {"line": 215, "column": 2}, "end": {"line": 217, "column": 3}}, "type": "if", "locations": [{"start": {"line": 215, "column": 2}, "end": {"line": 217, "column": 3}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\auth\\userManagement.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\auth\\userManagement.ts", "statementMap": {"0": {"start": {"line": 328, "column": 0}, "end": {"line": 328, "column": 16}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 65}}, "2": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 46}}, "3": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 56}}, "4": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 52}}, "5": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 24}}, "6": {"start": {"line": 14, "column": 26}, "end": {"line": 16, "column": 2}}, "7": {"start": {"line": 18, "column": 32}, "end": {"line": 36, "column": 2}}, "8": {"start": {"line": 38, "column": 24}, "end": {"line": 43, "column": 2}}, "9": {"start": {"line": 52, "column": 13}, "end": {"line": 121, "column": 2}}, "10": {"start": {"line": 60, "column": 4}, "end": {"line": 119, "column": 5}}, "11": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "12": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 76}}, "13": {"start": {"line": 66, "column": 23}, "end": {"line": 66, "column": 39}}, "14": {"start": {"line": 67, "column": 25}, "end": {"line": 67, "column": 48}}, "15": {"start": {"line": 70, "column": 25}, "end": {"line": 70, "column": 62}}, "16": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, "17": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 79}}, "18": {"start": {"line": 79, "column": 19}, "end": {"line": 79, "column": 28}}, "19": {"start": {"line": 80, "column": 17}, "end": {"line": 80, "column": 31}}, "20": {"start": {"line": 82, "column": 40}, "end": {"line": 85, "column": 8}}, "21": {"start": {"line": 83, "column": 41}, "end": {"line": 83, "column": 45}}, "22": {"start": {"line": 87, "column": 6}, "end": {"line": 89, "column": 7}}, "23": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 69}}, "24": {"start": {"line": 91, "column": 27}, "end": {"line": 91, "column": 54}}, "25": {"start": {"line": 92, "column": 28}, "end": {"line": 92, "column": 78}}, "26": {"start": {"line": 94, "column": 6}, "end": {"line": 108, "column": 8}}, "27": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 51}}, "28": {"start": {"line": 111, "column": 39}, "end": {"line": 111, "column": 51}}, "29": {"start": {"line": 113, "column": 6}, "end": {"line": 116, "column": 9}}, "30": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 68}}, "31": {"start": {"line": 126, "column": 13}, "end": {"line": 215, "column": 2}}, "32": {"start": {"line": 134, "column": 4}, "end": {"line": 213, "column": 5}}, "33": {"start": {"line": 136, "column": 6}, "end": {"line": 138, "column": 7}}, "34": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": 76}}, "35": {"start": {"line": 140, "column": 25}, "end": {"line": 140, "column": 48}}, "36": {"start": {"line": 143, "column": 6}, "end": {"line": 145, "column": 7}}, "37": {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 79}}, "38": {"start": {"line": 148, "column": 54}, "end": {"line": 148, "column": 89}}, "39": {"start": {"line": 150, "column": 17}, "end": {"line": 150, "column": 31}}, "40": {"start": {"line": 151, "column": 18}, "end": {"line": 151, "column": 69}}, "41": {"start": {"line": 154, "column": 6}, "end": {"line": 156, "column": 7}}, "42": {"start": {"line": 155, "column": 8}, "end": {"line": 155, "column": 48}}, "43": {"start": {"line": 159, "column": 6}, "end": {"line": 164, "column": 7}}, "44": {"start": {"line": 160, "column": 30}, "end": {"line": 160, "column": 80}}, "45": {"start": {"line": 161, "column": 8}, "end": {"line": 163, "column": 9}}, "46": {"start": {"line": 162, "column": 10}, "end": {"line": 162, "column": 50}}, "47": {"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": 33}}, "48": {"start": {"line": 169, "column": 23}, "end": {"line": 169, "column": 40}}, "49": {"start": {"line": 170, "column": 20}, "end": {"line": 170, "column": 22}}, "50": {"start": {"line": 172, "column": 6}, "end": {"line": 196, "column": 7}}, "51": {"start": {"line": 173, "column": 25}, "end": {"line": 173, "column": 35}}, "52": {"start": {"line": 176, "column": 8}, "end": {"line": 184, "column": 9}}, "53": {"start": {"line": 177, "column": 30}, "end": {"line": 177, "column": 54}}, "54": {"start": {"line": 178, "column": 29}, "end": {"line": 178, "column": 80}}, "55": {"start": {"line": 179, "column": 28}, "end": {"line": 179, "column": 85}}, "56": {"start": {"line": 181, "column": 10}, "end": {"line": 183, "column": 11}}, "57": {"start": {"line": 182, "column": 12}, "end": {"line": 182, "column": 21}}, "58": {"start": {"line": 186, "column": 8}, "end": {"line": 195, "column": 11}}, "59": {"start": {"line": 198, "column": 6}, "end": {"line": 202, "column": 8}}, "60": {"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 51}}, "61": {"start": {"line": 205, "column": 39}, "end": {"line": 205, "column": 51}}, "62": {"start": {"line": 207, "column": 6}, "end": {"line": 210, "column": 9}}, "63": {"start": {"line": 212, "column": 6}, "end": {"line": 212, "column": 68}}, "64": {"start": {"line": 220, "column": 13}, "end": {"line": 323, "column": 2}}, "65": {"start": {"line": 228, "column": 4}, "end": {"line": 321, "column": 5}}, "66": {"start": {"line": 230, "column": 6}, "end": {"line": 232, "column": 7}}, "67": {"start": {"line": 231, "column": 8}, "end": {"line": 231, "column": 76}}, "68": {"start": {"line": 234, "column": 23}, "end": {"line": 234, "column": 39}}, "69": {"start": {"line": 235, "column": 25}, "end": {"line": 235, "column": 48}}, "70": {"start": {"line": 238, "column": 51}, "end": {"line": 238, "column": 94}}, "71": {"start": {"line": 242, "column": 6}, "end": {"line": 244, "column": 7}}, "72": {"start": {"line": 243, "column": 8}, "end": {"line": 243, "column": 79}}, "73": {"start": {"line": 246, "column": 19}, "end": {"line": 246, "column": 28}}, "74": {"start": {"line": 247, "column": 17}, "end": {"line": 247, "column": 31}}, "75": {"start": {"line": 250, "column": 21}, "end": {"line": 298, "column": 8}}, "76": {"start": {"line": 252, "column": 24}, "end": {"line": 252, "column": 58}}, "77": {"start": {"line": 253, "column": 24}, "end": {"line": 253, "column": 54}}, "78": {"start": {"line": 255, "column": 8}, "end": {"line": 257, "column": 9}}, "79": {"start": {"line": 256, "column": 10}, "end": {"line": 256, "column": 71}}, "80": {"start": {"line": 259, "column": 29}, "end": {"line": 262, "column": 10}}, "81": {"start": {"line": 265, "column": 8}, "end": {"line": 268, "column": 9}}, "82": {"start": {"line": 266, "column": 10}, "end": {"line": 266, "column": 57}}, "83": {"start": {"line": 267, "column": 10}, "end": {"line": 267, "column": 44}}, "84": {"start": {"line": 271, "column": 8}, "end": {"line": 286, "column": 9}}, "85": {"start": {"line": 272, "column": 31}, "end": {"line": 272, "column": 64}}, "86": {"start": {"line": 273, "column": 10}, "end": {"line": 285, "column": 12}}, "87": {"start": {"line": 289, "column": 8}, "end": {"line": 289, "column": 53}}, "88": {"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 45}}, "89": {"start": {"line": 293, "column": 8}, "end": {"line": 297, "column": 10}}, "90": {"start": {"line": 300, "column": 6}, "end": {"line": 304, "column": 9}}, "91": {"start": {"line": 303, "column": 54}, "end": {"line": 303, "column": 68}}, "92": {"start": {"line": 306, "column": 6}, "end": {"line": 310, "column": 8}}, "93": {"start": {"line": 313, "column": 6}, "end": {"line": 313, "column": 51}}, "94": {"start": {"line": 313, "column": 39}, "end": {"line": 313, "column": 51}}, "95": {"start": {"line": 315, "column": 6}, "end": {"line": 318, "column": 9}}, "96": {"start": {"line": 320, "column": 6}, "end": {"line": 320, "column": 68}}, "97": {"start": {"line": 329, "column": 2}, "end": {"line": 340, "column": 3}}, "98": {"start": {"line": 331, "column": 6}, "end": {"line": 331, "column": 93}}, "99": {"start": {"line": 333, "column": 6}, "end": {"line": 333, "column": 65}}, "100": {"start": {"line": 335, "column": 6}, "end": {"line": 335, "column": 62}}, "101": {"start": {"line": 337, "column": 6}, "end": {"line": 337, "column": 34}}, "102": {"start": {"line": 339, "column": 6}, "end": {"line": 339, "column": 19}}}, "fnMap": {"0": {"name": "(anonymous_9)", "decl": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 7}}, "loc": {"start": {"line": 59, "column": 20}, "end": {"line": 120, "column": 3}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 83, "column": 35}, "end": {"line": 83, "column": 38}}, "loc": {"start": {"line": 83, "column": 41}, "end": {"line": 83, "column": 45}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 133, "column": 2}, "end": {"line": 133, "column": 7}}, "loc": {"start": {"line": 133, "column": 20}, "end": {"line": 214, "column": 3}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 227, "column": 2}, "end": {"line": 227, "column": 7}}, "loc": {"start": {"line": 227, "column": 20}, "end": {"line": 322, "column": 3}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 250, "column": 45}, "end": {"line": 250, "column": 50}}, "loc": {"start": {"line": 250, "column": 67}, "end": {"line": 298, "column": 7}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 303, "column": 49}, "end": {"line": 303, "column": 50}}, "loc": {"start": {"line": 303, "column": 54}, "end": {"line": 303, "column": 68}}}, "6": {"name": "hasPermission", "decl": {"start": {"line": 328, "column": 16}, "end": {"line": 328, "column": 29}}, "loc": {"start": {"line": 328, "column": 104}, "end": {"line": 341, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}]}, "1": {"loc": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, "type": "if", "locations": [{"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}]}, "2": {"loc": {"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 29}}, {"start": {"line": 74, "column": 33}, "end": {"line": 74, "column": 77}}]}, "3": {"loc": {"start": {"line": 87, "column": 6}, "end": {"line": 89, "column": 7}}, "type": "if", "locations": [{"start": {"line": 87, "column": 6}, "end": {"line": 89, "column": 7}}]}, "4": {"loc": {"start": {"line": 91, "column": 27}, "end": {"line": 91, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 27}, "end": {"line": 91, "column": 48}}, {"start": {"line": 91, "column": 52}, "end": {"line": 91, "column": 54}}]}, "5": {"loc": {"start": {"line": 92, "column": 28}, "end": {"line": 92, "column": 78}}, "type": "cond-expr", "locations": [{"start": {"line": 92, "column": 51}, "end": {"line": 92, "column": 71}}, {"start": {"line": 92, "column": 74}, "end": {"line": 92, "column": 78}}]}, "6": {"loc": {"start": {"line": 100, "column": 14}, "end": {"line": 100, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 14}, "end": {"line": 100, "column": 31}}, {"start": {"line": 100, "column": 35}, "end": {"line": 100, "column": 48}}]}, "7": {"loc": {"start": {"line": 105, "column": 18}, "end": {"line": 105, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 105, "column": 41}, "end": {"line": 105, "column": 45}}, {"start": {"line": 105, "column": 45}, "end": {"line": 105, "column": 49}}]}, "8": {"loc": {"start": {"line": 105, "column": 18}, "end": {"line": 105, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 18}, "end": {"line": 105, "column": 45}}, {"start": {"line": 105, "column": 41}, "end": {"line": 105, "column": 45}}]}, "9": {"loc": {"start": {"line": 105, "column": 18}, "end": {"line": 105, "column": 41}}, "type": "cond-expr", "locations": [{"start": {"line": 105, "column": 31}, "end": {"line": 105, "column": 33}}, {"start": {"line": 105, "column": 18}, "end": {"line": 105, "column": 41}}]}, "10": {"loc": {"start": {"line": 105, "column": 18}, "end": {"line": 105, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 18}, "end": {"line": 105, "column": 33}}, {"start": {"line": 105, "column": 18}, "end": {"line": 105, "column": 33}}]}, "11": {"loc": {"start": {"line": 106, "column": 21}, "end": {"line": 106, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 21}, "end": {"line": 106, "column": 47}}, {"start": {"line": 106, "column": 51}, "end": {"line": 106, "column": 53}}]}, "12": {"loc": {"start": {"line": 106, "column": 21}, "end": {"line": 106, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 106, "column": 34}, "end": {"line": 106, "column": 36}}, {"start": {"line": 106, "column": 21}, "end": {"line": 106, "column": 47}}]}, "13": {"loc": {"start": {"line": 106, "column": 21}, "end": {"line": 106, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 21}, "end": {"line": 106, "column": 36}}, {"start": {"line": 106, "column": 21}, "end": {"line": 106, "column": 36}}]}, "14": {"loc": {"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": 35}}, {"start": {"line": 107, "column": 39}, "end": {"line": 107, "column": 41}}]}, "15": {"loc": {"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": 35}}, "type": "cond-expr", "locations": [{"start": {"line": 107, "column": 28}, "end": {"line": 107, "column": 30}}, {"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": 35}}]}, "16": {"loc": {"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": 30}}, {"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": 30}}]}, "17": {"loc": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 51}}, "type": "if", "locations": [{"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 51}}]}, "18": {"loc": {"start": {"line": 114, "column": 15}, "end": {"line": 114, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 114, "column": 40}, "end": {"line": 114, "column": 53}}, {"start": {"line": 114, "column": 56}, "end": {"line": 114, "column": 69}}]}, "19": {"loc": {"start": {"line": 115, "column": 18}, "end": {"line": 115, "column": 35}}, "type": "cond-expr", "locations": [{"start": {"line": 115, "column": 30}, "end": {"line": 115, "column": 32}}, {"start": {"line": 115, "column": 30}, "end": {"line": 115, "column": 35}}]}, "20": {"loc": {"start": {"line": 115, "column": 18}, "end": {"line": 115, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 18}, "end": {"line": 115, "column": 32}}, {"start": {"line": 115, "column": 30}, "end": {"line": 115, "column": 32}}]}, "21": {"loc": {"start": {"line": 136, "column": 6}, "end": {"line": 138, "column": 7}}, "type": "if", "locations": [{"start": {"line": 136, "column": 6}, "end": {"line": 138, "column": 7}}]}, "22": {"loc": {"start": {"line": 143, "column": 6}, "end": {"line": 145, "column": 7}}, "type": "if", "locations": [{"start": {"line": 143, "column": 6}, "end": {"line": 145, "column": 7}}]}, "23": {"loc": {"start": {"line": 154, "column": 6}, "end": {"line": 156, "column": 7}}, "type": "if", "locations": [{"start": {"line": 154, "column": 6}, "end": {"line": 156, "column": 7}}]}, "24": {"loc": {"start": {"line": 159, "column": 6}, "end": {"line": 164, "column": 7}}, "type": "if", "locations": [{"start": {"line": 159, "column": 6}, "end": {"line": 164, "column": 7}}]}, "25": {"loc": {"start": {"line": 161, "column": 8}, "end": {"line": 163, "column": 9}}, "type": "if", "locations": [{"start": {"line": 161, "column": 8}, "end": {"line": 163, "column": 9}}]}, "26": {"loc": {"start": {"line": 176, "column": 8}, "end": {"line": 184, "column": 9}}, "type": "if", "locations": [{"start": {"line": 176, "column": 8}, "end": {"line": 184, "column": 9}}]}, "27": {"loc": {"start": {"line": 178, "column": 29}, "end": {"line": 178, "column": 80}}, "type": "cond-expr", "locations": [{"start": {"line": 178, "column": 43}, "end": {"line": 178, "column": 45}}, {"start": {"line": 178, "column": 43}, "end": {"line": 178, "column": 80}}]}, "28": {"loc": {"start": {"line": 178, "column": 29}, "end": {"line": 178, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 178, "column": 29}, "end": {"line": 178, "column": 45}}, {"start": {"line": 178, "column": 43}, "end": {"line": 178, "column": 45}}]}, "29": {"loc": {"start": {"line": 179, "column": 28}, "end": {"line": 179, "column": 85}}, "type": "cond-expr", "locations": [{"start": {"line": 179, "column": 48}, "end": {"line": 179, "column": 50}}, {"start": {"line": 179, "column": 48}, "end": {"line": 179, "column": 85}}]}, "30": {"loc": {"start": {"line": 179, "column": 28}, "end": {"line": 179, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 179, "column": 28}, "end": {"line": 179, "column": 50}}, {"start": {"line": 179, "column": 48}, "end": {"line": 179, "column": 50}}]}, "31": {"loc": {"start": {"line": 181, "column": 10}, "end": {"line": 183, "column": 11}}, "type": "if", "locations": [{"start": {"line": 181, "column": 10}, "end": {"line": 183, "column": 11}}]}, "32": {"loc": {"start": {"line": 181, "column": 14}, "end": {"line": 181, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 181, "column": 14}, "end": {"line": 181, "column": 25}}, {"start": {"line": 181, "column": 29}, "end": {"line": 181, "column": 39}}]}, "33": {"loc": {"start": {"line": 193, "column": 26}, "end": {"line": 193, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 193, "column": 40}, "end": {"line": 193, "column": 42}}, {"start": {"line": 193, "column": 40}, "end": {"line": 193, "column": 56}}]}, "34": {"loc": {"start": {"line": 193, "column": 26}, "end": {"line": 193, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 193, "column": 26}, "end": {"line": 193, "column": 42}}, {"start": {"line": 193, "column": 40}, "end": {"line": 193, "column": 42}}]}, "35": {"loc": {"start": {"line": 194, "column": 25}, "end": {"line": 194, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 194, "column": 25}, "end": {"line": 194, "column": 54}}, {"start": {"line": 194, "column": 58}, "end": {"line": 194, "column": 59}}]}, "36": {"loc": {"start": {"line": 194, "column": 25}, "end": {"line": 194, "column": 54}}, "type": "cond-expr", "locations": [{"start": {"line": 194, "column": 39}, "end": {"line": 194, "column": 41}}, {"start": {"line": 194, "column": 39}, "end": {"line": 194, "column": 54}}]}, "37": {"loc": {"start": {"line": 194, "column": 25}, "end": {"line": 194, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 194, "column": 25}, "end": {"line": 194, "column": 41}}, {"start": {"line": 194, "column": 39}, "end": {"line": 194, "column": 41}}]}, "38": {"loc": {"start": {"line": 201, "column": 20}, "end": {"line": 201, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 201, "column": 59}, "end": {"line": 201, "column": 61}}, {"start": {"line": 201, "column": 59}, "end": {"line": 201, "column": 63}}]}, "39": {"loc": {"start": {"line": 201, "column": 20}, "end": {"line": 201, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 201, "column": 20}, "end": {"line": 201, "column": 61}}, {"start": {"line": 201, "column": 59}, "end": {"line": 201, "column": 61}}]}, "40": {"loc": {"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 51}}, "type": "if", "locations": [{"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 51}}]}, "41": {"loc": {"start": {"line": 208, "column": 15}, "end": {"line": 208, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 208, "column": 40}, "end": {"line": 208, "column": 53}}, {"start": {"line": 208, "column": 56}, "end": {"line": 208, "column": 69}}]}, "42": {"loc": {"start": {"line": 209, "column": 18}, "end": {"line": 209, "column": 35}}, "type": "cond-expr", "locations": [{"start": {"line": 209, "column": 30}, "end": {"line": 209, "column": 32}}, {"start": {"line": 209, "column": 30}, "end": {"line": 209, "column": 35}}]}, "43": {"loc": {"start": {"line": 209, "column": 18}, "end": {"line": 209, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 209, "column": 18}, "end": {"line": 209, "column": 32}}, {"start": {"line": 209, "column": 30}, "end": {"line": 209, "column": 32}}]}, "44": {"loc": {"start": {"line": 230, "column": 6}, "end": {"line": 232, "column": 7}}, "type": "if", "locations": [{"start": {"line": 230, "column": 6}, "end": {"line": 232, "column": 7}}]}, "45": {"loc": {"start": {"line": 242, "column": 6}, "end": {"line": 244, "column": 7}}, "type": "if", "locations": [{"start": {"line": 242, "column": 6}, "end": {"line": 244, "column": 7}}]}, "46": {"loc": {"start": {"line": 242, "column": 10}, "end": {"line": 242, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 242, "column": 10}, "end": {"line": 242, "column": 29}}, {"start": {"line": 242, "column": 33}, "end": {"line": 242, "column": 55}}]}, "47": {"loc": {"start": {"line": 255, "column": 8}, "end": {"line": 257, "column": 9}}, "type": "if", "locations": [{"start": {"line": 255, "column": 8}, "end": {"line": 257, "column": 9}}]}, "48": {"loc": {"start": {"line": 265, "column": 8}, "end": {"line": 268, "column": 9}}, "type": "if", "locations": [{"start": {"line": 265, "column": 8}, "end": {"line": 268, "column": 9}}]}, "49": {"loc": {"start": {"line": 271, "column": 8}, "end": {"line": 286, "column": 9}}, "type": "if", "locations": [{"start": {"line": 271, "column": 8}, "end": {"line": 286, "column": 9}}]}, "50": {"loc": {"start": {"line": 272, "column": 31}, "end": {"line": 272, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 272, "column": 31}, "end": {"line": 272, "column": 58}}, {"start": {"line": 272, "column": 62}, "end": {"line": 272, "column": 64}}]}, "51": {"loc": {"start": {"line": 272, "column": 31}, "end": {"line": 272, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 272, "column": 45}, "end": {"line": 272, "column": 47}}, {"start": {"line": 272, "column": 45}, "end": {"line": 272, "column": 58}}]}, "52": {"loc": {"start": {"line": 272, "column": 31}, "end": {"line": 272, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 272, "column": 31}, "end": {"line": 272, "column": 47}}, {"start": {"line": 272, "column": 45}, "end": {"line": 272, "column": 47}}]}, "53": {"loc": {"start": {"line": 295, "column": 23}, "end": {"line": 295, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 295, "column": 23}, "end": {"line": 295, "column": 34}}, {"start": {"line": 295, "column": 38}, "end": {"line": 295, "column": 65}}]}, "54": {"loc": {"start": {"line": 295, "column": 38}, "end": {"line": 295, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 295, "column": 52}, "end": {"line": 295, "column": 54}}, {"start": {"line": 295, "column": 52}, "end": {"line": 295, "column": 65}}]}, "55": {"loc": {"start": {"line": 295, "column": 38}, "end": {"line": 295, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 295, "column": 38}, "end": {"line": 295, "column": 54}}, {"start": {"line": 295, "column": 52}, "end": {"line": 295, "column": 54}}]}, "56": {"loc": {"start": {"line": 296, "column": 23}, "end": {"line": 296, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 296, "column": 23}, "end": {"line": 296, "column": 42}}, {"start": {"line": 296, "column": 46}, "end": {"line": 296, "column": 73}}]}, "57": {"loc": {"start": {"line": 296, "column": 46}, "end": {"line": 296, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 296, "column": 60}, "end": {"line": 296, "column": 62}}, {"start": {"line": 296, "column": 60}, "end": {"line": 296, "column": 73}}]}, "58": {"loc": {"start": {"line": 296, "column": 46}, "end": {"line": 296, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 296, "column": 46}, "end": {"line": 296, "column": 62}}, {"start": {"line": 296, "column": 60}, "end": {"line": 296, "column": 62}}]}, "59": {"loc": {"start": {"line": 313, "column": 6}, "end": {"line": 313, "column": 51}}, "type": "if", "locations": [{"start": {"line": 313, "column": 6}, "end": {"line": 313, "column": 51}}]}, "60": {"loc": {"start": {"line": 316, "column": 15}, "end": {"line": 316, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 316, "column": 40}, "end": {"line": 316, "column": 53}}, {"start": {"line": 316, "column": 56}, "end": {"line": 316, "column": 69}}]}, "61": {"loc": {"start": {"line": 317, "column": 18}, "end": {"line": 317, "column": 35}}, "type": "cond-expr", "locations": [{"start": {"line": 317, "column": 30}, "end": {"line": 317, "column": 32}}, {"start": {"line": 317, "column": 30}, "end": {"line": 317, "column": 35}}]}, "62": {"loc": {"start": {"line": 317, "column": 18}, "end": {"line": 317, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 317, "column": 18}, "end": {"line": 317, "column": 32}}, {"start": {"line": 317, "column": 30}, "end": {"line": 317, "column": 32}}]}, "63": {"loc": {"start": {"line": 329, "column": 2}, "end": {"line": 340, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 330, "column": 4}, "end": {"line": 331, "column": 93}}, {"start": {"line": 332, "column": 4}, "end": {"line": 333, "column": 65}}, {"start": {"line": 334, "column": 4}, "end": {"line": 335, "column": 62}}, {"start": {"line": 336, "column": 4}, "end": {"line": 337, "column": 34}}, {"start": {"line": 338, "column": 4}, "end": {"line": 339, "column": 19}}]}, "64": {"loc": {"start": {"line": 331, "column": 13}, "end": {"line": 331, "column": 92}}, "type": "binary-expr", "locations": [{"start": {"line": 331, "column": 13}, "end": {"line": 331, "column": 33}}, {"start": {"line": 331, "column": 37}, "end": {"line": 331, "column": 61}}, {"start": {"line": 331, "column": 66}, "end": {"line": 331, "column": 91}}]}, "65": {"loc": {"start": {"line": 333, "column": 13}, "end": {"line": 333, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 333, "column": 13}, "end": {"line": 333, "column": 33}}, {"start": {"line": 333, "column": 38}, "end": {"line": 333, "column": 63}}]}, "66": {"loc": {"start": {"line": 335, "column": 13}, "end": {"line": 335, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 335, "column": 13}, "end": {"line": 335, "column": 33}}, {"start": {"line": 335, "column": 37}, "end": {"line": 335, "column": 61}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0], "22": [0], "23": [0], "24": [0], "25": [0], "26": [0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0], "41": [0, 0], "42": [0, 0], "43": [0, 0], "44": [0], "45": [0], "46": [0, 0], "47": [0], "48": [0], "49": [0], "50": [0, 0], "51": [0, 0], "52": [0, 0], "53": [0, 0], "54": [0, 0], "55": [0, 0], "56": [0, 0], "57": [0, 0], "58": [0, 0], "59": [0], "60": [0, 0], "61": [0, 0], "62": [0, 0], "63": [0, 0, 0, 0, 0], "64": [0, 0, 0], "65": [0, 0], "66": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\audit.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\audit.ts", "statementMap": {"0": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 7}}, "1": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 7}}, "2": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 7}}, "3": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 7}}, "4": {"start": {"line": 250, "column": 0}, "end": {"line": 250, "column": 7}}, "5": {"start": {"line": 291, "column": 0}, "end": {"line": 291, "column": 7}}, "6": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 68}}, "7": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 44}}, "8": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": null}}, "9": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": null}}, "10": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": null}}, "11": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "12": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": null}}, "13": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": null}}, "14": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": null}}, "15": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": null}}, "16": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": null}}, "17": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": null}}, "18": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": null}}, "19": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": null}}, "20": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": null}}, "21": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": null}}, "22": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": null}}, "23": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": null}}, "24": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "25": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": null}}, "26": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": null}}, "27": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": null}}, "28": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": null}}, "29": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": null}}, "30": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": null}}, "31": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": null}}, "32": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": null}}, "33": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": null}}, "34": {"start": {"line": 84, "column": 2}, "end": {"line": 120, "column": 3}}, "35": {"start": {"line": 85, "column": 15}, "end": {"line": 85, "column": 29}}, "36": {"start": {"line": 87, "column": 31}, "end": {"line": 100, "column": 6}}, "37": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 51}}, "38": {"start": {"line": 106, "column": 4}, "end": {"line": 111, "column": 7}}, "39": {"start": {"line": 115, "column": 4}, "end": {"line": 119, "column": 7}}, "40": {"start": {"line": 138, "column": 2}, "end": {"line": 178, "column": 3}}, "41": {"start": {"line": 139, "column": 15}, "end": {"line": 139, "column": 29}}, "42": {"start": {"line": 141, "column": 24}, "end": {"line": 158, "column": 6}}, "43": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 54}}, "44": {"start": {"line": 163, "column": 4}, "end": {"line": 170, "column": 7}}, "45": {"start": {"line": 173, "column": 4}, "end": {"line": 177, "column": 7}}, "46": {"start": {"line": 199, "column": 2}, "end": {"line": 209, "column": 5}}, "47": {"start": {"line": 225, "column": 2}, "end": {"line": 237, "column": 3}}, "48": {"start": {"line": 226, "column": 4}, "end": {"line": 236, "column": 7}}, "49": {"start": {"line": 239, "column": 2}, "end": {"line": 244, "column": 5}}, "50": {"start": {"line": 251, "column": 2}, "end": {"line": 285, "column": 3}}, "51": {"start": {"line": 252, "column": 15}, "end": {"line": 252, "column": 29}}, "52": {"start": {"line": 253, "column": 23}, "end": {"line": 253, "column": 33}}, "53": {"start": {"line": 254, "column": 4}, "end": {"line": 254, "column": 61}}, "54": {"start": {"line": 256, "column": 25}, "end": {"line": 258, "column": 17}}, "55": {"start": {"line": 260, "column": 21}, "end": {"line": 260, "column": 45}}, "56": {"start": {"line": 262, "column": 4}, "end": {"line": 264, "column": 5}}, "57": {"start": {"line": 263, "column": 6}, "end": {"line": 263, "column": 15}}, "58": {"start": {"line": 266, "column": 18}, "end": {"line": 266, "column": 28}}, "59": {"start": {"line": 267, "column": 4}, "end": {"line": 269, "column": 7}}, "60": {"start": {"line": 268, "column": 6}, "end": {"line": 268, "column": 28}}, "61": {"start": {"line": 271, "column": 4}, "end": {"line": 271, "column": 25}}, "62": {"start": {"line": 273, "column": 4}, "end": {"line": 276, "column": 7}}, "63": {"start": {"line": 278, "column": 4}, "end": {"line": 278, "column": 25}}, "64": {"start": {"line": 281, "column": 4}, "end": {"line": 283, "column": 7}}, "65": {"start": {"line": 284, "column": 4}, "end": {"line": 284, "column": 16}}, "66": {"start": {"line": 301, "column": 2}, "end": {"line": 356, "column": 3}}, "67": {"start": {"line": 302, "column": 15}, "end": {"line": 302, "column": 29}}, "68": {"start": {"line": 304, "column": 22}, "end": {"line": 306, "column": 40}}, "69": {"start": {"line": 308, "column": 21}, "end": {"line": 308, "column": 42}}, "70": {"start": {"line": 310, "column": 18}, "end": {"line": 316, "column": 6}}, "71": {"start": {"line": 318, "column": 4}, "end": {"line": 335, "column": 7}}, "72": {"start": {"line": 319, "column": 19}, "end": {"line": 319, "column": 41}}, "73": {"start": {"line": 321, "column": 6}, "end": {"line": 325, "column": 7}}, "74": {"start": {"line": 322, "column": 8}, "end": {"line": 322, "column": 33}}, "75": {"start": {"line": 324, "column": 8}, "end": {"line": 324, "column": 29}}, "76": {"start": {"line": 328, "column": 26}, "end": {"line": 328, "column": 64}}, "77": {"start": {"line": 329, "column": 6}, "end": {"line": 329, "column": 57}}, "78": {"start": {"line": 332, "column": 24}, "end": {"line": 332, "column": 98}}, "79": {"start": {"line": 333, "column": 6}, "end": {"line": 333, "column": 24}}, "80": {"start": {"line": 334, "column": 6}, "end": {"line": 334, "column": 49}}, "81": {"start": {"line": 337, "column": 4}, "end": {"line": 349, "column": 6}}, "82": {"start": {"line": 342, "column": 35}, "end": {"line": 342, "column": 52}}, "83": {"start": {"line": 343, "column": 24}, "end": {"line": 343, "column": 41}}, "84": {"start": {"line": 346, "column": 34}, "end": {"line": 346, "column": 90}}, "85": {"start": {"line": 347, "column": 24}, "end": {"line": 347, "column": 41}}, "86": {"start": {"line": 352, "column": 4}, "end": {"line": 354, "column": 7}}, "87": {"start": {"line": 355, "column": 4}, "end": {"line": 355, "column": 16}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 12}}, "loc": {"start": {"line": 27, "column": 23}, "end": {"line": 66, "column": 1}}}, "1": {"name": "logAuditEvent", "decl": {"start": {"line": 71, "column": 22}, "end": {"line": 71, "column": 35}}, "loc": {"start": {"line": 82, "column": 8}, "end": {"line": 121, "column": 1}}}, "2": {"name": "logUnauthorizedAccess", "decl": {"start": {"line": 126, "column": 22}, "end": {"line": 126, "column": 43}}, "loc": {"start": {"line": 136, "column": 3}, "end": {"line": 179, "column": 1}}}, "3": {"name": "logDataChange", "decl": {"start": {"line": 184, "column": 22}, "end": {"line": 184, "column": 35}}, "loc": {"start": {"line": 197, "column": 8}, "end": {"line": 210, "column": 1}}}, "4": {"name": "logValidationError", "decl": {"start": {"line": 215, "column": 22}, "end": {"line": 215, "column": 40}}, "loc": {"start": {"line": 223, "column": 8}, "end": {"line": 245, "column": 1}}}, "5": {"name": "cleanupOldAuditLogs", "decl": {"start": {"line": 250, "column": 22}, "end": {"line": 250, "column": 41}}, "loc": {"start": {"line": 250, "column": 69}, "end": {"line": 286, "column": 1}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 267, "column": 26}, "end": {"line": 267, "column": 29}}, "loc": {"start": {"line": 267, "column": 32}, "end": {"line": 269, "column": 5}}}, "7": {"name": "getAuditStats", "decl": {"start": {"line": 291, "column": 22}, "end": {"line": 291, "column": 35}}, "loc": {"start": {"line": 293, "column": 15}, "end": {"line": 357, "column": 1}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 318, "column": 26}, "end": {"line": 318, "column": 29}}, "loc": {"start": {"line": 318, "column": 32}, "end": {"line": 335, "column": 5}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 342, "column": 13}, "end": {"line": 342, "column": 14}}, "loc": {"start": {"line": 342, "column": 35}, "end": {"line": 342, "column": 52}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 343, "column": 14}, "end": {"line": 343, "column": 15}}, "loc": {"start": {"line": 343, "column": 24}, "end": {"line": 343, "column": 41}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 346, "column": 13}, "end": {"line": 346, "column": 14}}, "loc": {"start": {"line": 346, "column": 34}, "end": {"line": 346, "column": 90}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 347, "column": 14}, "end": {"line": 347, "column": 15}}, "loc": {"start": {"line": 347, "column": 24}, "end": {"line": 347, "column": 41}}}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 12}, "end": {"line": 27, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 27, "column": 12}, "end": {"line": 27, "column": 23}}, {"start": {"line": 27, "column": 23}, "end": {"line": 27, "column": null}}]}, "1": {"loc": {"start": {"line": 74, "column": 2}, "end": {"line": 82, "column": 8}}, "type": "default-arg", "locations": [{"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 8}}]}, "2": {"loc": {"start": {"line": 98, "column": 15}, "end": {"line": 98, "column": 38}}, "type": "cond-expr", "locations": [{"start": {"line": 98, "column": 30}, "end": {"line": 98, "column": 34}}, {"start": {"line": 98, "column": 34}, "end": {"line": 98, "column": 38}}]}, "3": {"loc": {"start": {"line": 98, "column": 15}, "end": {"line": 98, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 98, "column": 15}, "end": {"line": 98, "column": 34}}, {"start": {"line": 98, "column": 30}, "end": {"line": 98, "column": 34}}]}, "4": {"loc": {"start": {"line": 110, "column": 15}, "end": {"line": 110, "column": 38}}, "type": "cond-expr", "locations": [{"start": {"line": 110, "column": 30}, "end": {"line": 110, "column": 34}}, {"start": {"line": 110, "column": 34}, "end": {"line": 110, "column": 38}}]}, "5": {"loc": {"start": {"line": 110, "column": 15}, "end": {"line": 110, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 15}, "end": {"line": 110, "column": 34}}, {"start": {"line": 110, "column": 30}, "end": {"line": 110, "column": 34}}]}, "6": {"loc": {"start": {"line": 118, "column": 13}, "end": {"line": 118, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 118, "column": 38}, "end": {"line": 118, "column": 51}}, {"start": {"line": 118, "column": 54}, "end": {"line": 118, "column": 71}}]}, "7": {"loc": {"start": {"line": 142, "column": 14}, "end": {"line": 142, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 142, "column": 14}, "end": {"line": 142, "column": 20}}, {"start": {"line": 142, "column": 24}, "end": {"line": 142, "column": 35}}]}, "8": {"loc": {"start": {"line": 143, "column": 17}, "end": {"line": 143, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 143, "column": 36}, "end": {"line": 143, "column": 45}}, {"start": {"line": 143, "column": 48}, "end": {"line": 143, "column": 52}}]}, "9": {"loc": {"start": {"line": 144, "column": 16}, "end": {"line": 144, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 144, "column": 16}, "end": {"line": 144, "column": 32}}, {"start": {"line": 144, "column": 36}, "end": {"line": 144, "column": 42}}]}, "10": {"loc": {"start": {"line": 164, "column": 14}, "end": {"line": 164, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 164, "column": 14}, "end": {"line": 164, "column": 20}}, {"start": {"line": 164, "column": 24}, "end": {"line": 164, "column": 35}}]}, "11": {"loc": {"start": {"line": 174, "column": 14}, "end": {"line": 174, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 174, "column": 14}, "end": {"line": 174, "column": 20}}, {"start": {"line": 174, "column": 24}, "end": {"line": 174, "column": 35}}]}, "12": {"loc": {"start": {"line": 176, "column": 13}, "end": {"line": 176, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 176, "column": 38}, "end": {"line": 176, "column": 51}}, {"start": {"line": 176, "column": 54}, "end": {"line": 176, "column": 71}}]}, "13": {"loc": {"start": {"line": 194, "column": 2}, "end": {"line": 197, "column": 8}}, "type": "default-arg", "locations": [{"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 8}}]}, "14": {"loc": {"start": {"line": 220, "column": 2}, "end": {"line": 223, "column": 8}}, "type": "default-arg", "locations": [{"start": {"line": 223, "column": 6}, "end": {"line": 223, "column": 8}}]}, "15": {"loc": {"start": {"line": 225, "column": 2}, "end": {"line": 237, "column": 3}}, "type": "if", "locations": [{"start": {"line": 225, "column": 2}, "end": {"line": 237, "column": 3}}]}, "16": {"loc": {"start": {"line": 230, "column": 19}, "end": {"line": 230, "column": 68}}, "type": "cond-expr", "locations": [{"start": {"line": 230, "column": 31}, "end": {"line": 230, "column": 56}}, {"start": {"line": 230, "column": 59}, "end": {"line": 230, "column": 68}}]}, "17": {"loc": {"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 21}}, {"start": {"line": 240, "column": 25}, "end": {"line": 240, "column": 36}}]}, "18": {"loc": {"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 21}}, "type": "cond-expr", "locations": [{"start": {"line": 240, "column": 16}, "end": {"line": 240, "column": 18}}, {"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 21}}]}, "19": {"loc": {"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 18}}, "type": "binary-expr", "locations": [{"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 18}}, {"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 18}}]}, "20": {"loc": {"start": {"line": 250, "column": 42}, "end": {"line": 250, "column": 69}}, "type": "default-arg", "locations": [{"start": {"line": 250, "column": 66}, "end": {"line": 250, "column": 69}}]}, "21": {"loc": {"start": {"line": 262, "column": 4}, "end": {"line": 264, "column": 5}}, "type": "if", "locations": [{"start": {"line": 262, "column": 4}, "end": {"line": 264, "column": 5}}]}, "22": {"loc": {"start": {"line": 282, "column": 13}, "end": {"line": 282, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 282, "column": 38}, "end": {"line": 282, "column": 51}}, {"start": {"line": 282, "column": 54}, "end": {"line": 282, "column": 71}}]}, "23": {"loc": {"start": {"line": 321, "column": 6}, "end": {"line": 325, "column": 7}}, "type": "if", "locations": [{"start": {"line": 321, "column": 6}, "end": {"line": 325, "column": 7}}, {"start": {"line": 323, "column": 13}, "end": {"line": 325, "column": 7}}]}, "24": {"loc": {"start": {"line": 328, "column": 26}, "end": {"line": 328, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 328, "column": 26}, "end": {"line": 328, "column": 59}}, {"start": {"line": 328, "column": 63}, "end": {"line": 328, "column": 64}}]}, "25": {"loc": {"start": {"line": 332, "column": 24}, "end": {"line": 332, "column": 98}}, "type": "binary-expr", "locations": [{"start": {"line": 332, "column": 24}, "end": {"line": 332, "column": 55}}, {"start": {"line": 332, "column": 59}, "end": {"line": 332, "column": 98}}]}, "26": {"loc": {"start": {"line": 353, "column": 13}, "end": {"line": 353, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 353, "column": 38}, "end": {"line": 353, "column": 51}}, {"start": {"line": 353, "column": 54}, "end": {"line": 353, "column": 71}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0], "14": [0], "15": [0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0], "21": [0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\auth.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\auth.ts", "statementMap": {"0": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 16}}, "1": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 16}}, "2": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 16}}, "3": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 16}}, "4": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 16}}, "5": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 16}}, "6": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 16}}, "7": {"start": {"line": 229, "column": 0}, "end": {"line": 229, "column": 16}}, "8": {"start": {"line": 252, "column": 0}, "end": {"line": 252, "column": 16}}, "9": {"start": {"line": 280, "column": 0}, "end": {"line": 280, "column": 7}}, "10": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 44}}, "11": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 46}}, "12": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 19}}, "13": {"start": {"line": 33, "column": 11}, "end": {"line": 33, "column": 26}}, "14": {"start": {"line": 34, "column": 11}, "end": {"line": 34, "column": 32}}, "15": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 28}}, "16": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 13}}, "17": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 19}}, "18": {"start": {"line": 47, "column": 11}, "end": {"line": 47, "column": 26}}, "19": {"start": {"line": 48, "column": 11}, "end": {"line": 48, "column": 32}}, "20": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 37}}, "21": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 13}}, "22": {"start": {"line": 59, "column": 2}, "end": {"line": 66, "column": 3}}, "23": {"start": {"line": 60, "column": 4}, "end": {"line": 64, "column": 7}}, "24": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 52}}, "25": {"start": {"line": 68, "column": 25}, "end": {"line": 68, "column": 37}}, "26": {"start": {"line": 69, "column": 15}, "end": {"line": 69, "column": 37}}, "27": {"start": {"line": 71, "column": 2}, "end": {"line": 79, "column": 3}}, "28": {"start": {"line": 72, "column": 4}, "end": {"line": 77, "column": 7}}, "29": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 62}}, "30": {"start": {"line": 81, "column": 2}, "end": {"line": 86, "column": 4}}, "31": {"start": {"line": 93, "column": 15}, "end": {"line": 93, "column": 35}}, "32": {"start": {"line": 95, "column": 2}, "end": {"line": 106, "column": 3}}, "33": {"start": {"line": 96, "column": 4}, "end": {"line": 104, "column": 7}}, "34": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 79}}, "35": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": 14}}, "36": {"start": {"line": 115, "column": 15}, "end": {"line": 115, "column": 35}}, "37": {"start": {"line": 117, "column": 2}, "end": {"line": 128, "column": 3}}, "38": {"start": {"line": 117, "column": 33}, "end": {"line": 117, "column": 57}}, "39": {"start": {"line": 118, "column": 4}, "end": {"line": 126, "column": 7}}, "40": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 94}}, "41": {"start": {"line": 130, "column": 2}, "end": {"line": 130, "column": 14}}, "42": {"start": {"line": 138, "column": 50}, "end": {"line": 142, "column": 4}}, "43": {"start": {"line": 144, "column": 2}, "end": {"line": 144, "column": 64}}, "44": {"start": {"line": 151, "column": 15}, "end": {"line": 151, "column": 35}}, "45": {"start": {"line": 154, "column": 2}, "end": {"line": 156, "column": 3}}, "46": {"start": {"line": 155, "column": 4}, "end": {"line": 155, "column": 16}}, "47": {"start": {"line": 158, "column": 2}, "end": {"line": 168, "column": 3}}, "48": {"start": {"line": 159, "column": 4}, "end": {"line": 166, "column": 7}}, "49": {"start": {"line": 167, "column": 4}, "end": {"line": 167, "column": 100}}, "50": {"start": {"line": 170, "column": 2}, "end": {"line": 170, "column": 14}}, "51": {"start": {"line": 182, "column": 15}, "end": {"line": 182, "column": 35}}, "52": {"start": {"line": 185, "column": 2}, "end": {"line": 187, "column": 3}}, "53": {"start": {"line": 186, "column": 4}, "end": {"line": 186, "column": 16}}, "54": {"start": {"line": 190, "column": 2}, "end": {"line": 201, "column": 3}}, "55": {"start": {"line": 191, "column": 4}, "end": {"line": 199, "column": 7}}, "56": {"start": {"line": 200, "column": 4}, "end": {"line": 200, "column": 107}}, "57": {"start": {"line": 203, "column": 2}, "end": {"line": 203, "column": 14}}, "58": {"start": {"line": 215, "column": 2}, "end": {"line": 223, "column": 5}}, "59": {"start": {"line": 234, "column": 2}, "end": {"line": 246, "column": 3}}, "60": {"start": {"line": 235, "column": 4}, "end": {"line": 235, "column": 27}}, "61": {"start": {"line": 237, "column": 4}, "end": {"line": 244, "column": 7}}, "62": {"start": {"line": 245, "column": 4}, "end": {"line": 245, "column": 51}}, "63": {"start": {"line": 256, "column": 2}, "end": {"line": 274, "column": 4}}, "64": {"start": {"line": 257, "column": 17}, "end": {"line": 257, "column": 89}}, "65": {"start": {"line": 259, "column": 4}, "end": {"line": 273, "column": 5}}, "66": {"start": {"line": 260, "column": 21}, "end": {"line": 260, "column": 49}}, "67": {"start": {"line": 261, "column": 6}, "end": {"line": 261, "column": 80}}, "68": {"start": {"line": 262, "column": 6}, "end": {"line": 262, "column": 20}}, "69": {"start": {"line": 264, "column": 6}, "end": {"line": 271, "column": 9}}, "70": {"start": {"line": 272, "column": 6}, "end": {"line": 272, "column": 18}}, "71": {"start": {"line": 287, "column": 2}, "end": {"line": 305, "column": 3}}, "72": {"start": {"line": 288, "column": 23}, "end": {"line": 288, "column": 51}}, "73": {"start": {"line": 289, "column": 17}, "end": {"line": 289, "column": 77}}, "74": {"start": {"line": 291, "column": 4}, "end": {"line": 297, "column": 6}}, "75": {"start": {"line": 299, "column": 4}, "end": {"line": 303, "column": 7}}, "76": {"start": {"line": 304, "column": 4}, "end": {"line": 304, "column": 47}}, "77": {"start": {"line": 311, "column": 13}, "end": {"line": 318, "column": 11}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "loc": {"start": {"line": 34, "column": 35}, "end": {"line": 38, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": null}}, "loc": {"start": {"line": 48, "column": 35}, "end": {"line": 52, "column": 3}}}, "2": {"name": "requireAuth", "decl": {"start": {"line": 58, "column": 16}, "end": {"line": 58, "column": 27}}, "loc": {"start": {"line": 58, "column": 52}, "end": {"line": 87, "column": 1}}}, "3": {"name": "requireRole", "decl": {"start": {"line": 92, "column": 16}, "end": {"line": 92, "column": 27}}, "loc": {"start": {"line": 92, "column": 76}, "end": {"line": 109, "column": 1}}}, "4": {"name": "requireAnyRole", "decl": {"start": {"line": 114, "column": 16}, "end": {"line": 114, "column": 30}}, "loc": {"start": {"line": 114, "column": 81}, "end": {"line": 131, "column": 1}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 117, "column": 25}, "end": {"line": 117, "column": 29}}, "loc": {"start": {"line": 117, "column": 33}, "end": {"line": 117, "column": 57}}}, "6": {"name": "hasRole", "decl": {"start": {"line": 137, "column": 16}, "end": {"line": 137, "column": 23}}, "loc": {"start": {"line": 137, "column": 66}, "end": {"line": 145, "column": 1}}}, "7": {"name": "requireOwnership", "decl": {"start": {"line": 150, "column": 16}, "end": {"line": 150, "column": 32}}, "loc": {"start": {"line": 150, "column": 82}, "end": {"line": 171, "column": 1}}}, "8": {"name": "requireModifyPermission", "decl": {"start": {"line": 177, "column": 16}, "end": {"line": 177, "column": 39}}, "loc": {"start": {"line": 180, "column": 37}, "end": {"line": 204, "column": 1}}}, "9": {"name": "logSuccessfulAccess", "decl": {"start": {"line": 209, "column": 16}, "end": {"line": 209, "column": 35}}, "loc": {"start": {"line": 213, "column": 38}, "end": {"line": 224, "column": 1}}}, "10": {"name": "validateAndSanitizeInput", "decl": {"start": {"line": 229, "column": 16}, "end": {"line": 229, "column": 40}}, "loc": {"start": {"line": 232, "column": 26}, "end": {"line": 247, "column": 1}}}, "11": {"name": "<PERSON><PERSON><PERSON>", "decl": {"start": {"line": 252, "column": 16}, "end": {"line": 252, "column": 24}}, "loc": {"start": {"line": 254, "column": 25}, "end": {"line": 275, "column": 1}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 256, "column": 9}, "end": {"line": 256, "column": 14}}, "loc": {"start": {"line": 256, "column": 68}, "end": {"line": 274, "column": 3}}}, "13": {"name": "getUserInfo", "decl": {"start": {"line": 280, "column": 22}, "end": {"line": 280, "column": 33}}, "loc": {"start": {"line": 280, "column": 45}, "end": {"line": 306, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 11}, "end": {"line": 33, "column": 43}}, "type": "default-arg", "locations": [{"start": {"line": 33, "column": 26}, "end": {"line": 33, "column": 43}}]}, "1": {"loc": {"start": {"line": 34, "column": 11}, "end": {"line": 34, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 34, "column": 32}, "end": {"line": 34, "column": 35}}]}, "2": {"loc": {"start": {"line": 47, "column": 11}, "end": {"line": 47, "column": 45}}, "type": "default-arg", "locations": [{"start": {"line": 47, "column": 26}, "end": {"line": 47, "column": 45}}]}, "3": {"loc": {"start": {"line": 48, "column": 11}, "end": {"line": 48, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 48, "column": 32}, "end": {"line": 48, "column": 35}}]}, "4": {"loc": {"start": {"line": 59, "column": 2}, "end": {"line": 66, "column": 3}}, "type": "if", "locations": [{"start": {"line": 59, "column": 2}, "end": {"line": 66, "column": 3}}]}, "5": {"loc": {"start": {"line": 62, "column": 10}, "end": {"line": 62, "column": 32}}, "type": "cond-expr", "locations": [{"start": {"line": 62, "column": 28}, "end": {"line": 62, "column": 30}}, {"start": {"line": 62, "column": 28}, "end": {"line": 62, "column": 32}}]}, "6": {"loc": {"start": {"line": 62, "column": 10}, "end": {"line": 62, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 62, "column": 10}, "end": {"line": 62, "column": 30}}, {"start": {"line": 62, "column": 28}, "end": {"line": 62, "column": 30}}]}, "7": {"loc": {"start": {"line": 63, "column": 17}, "end": {"line": 63, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 35}, "end": {"line": 63, "column": 37}}, {"start": {"line": 63, "column": 35}, "end": {"line": 63, "column": 58}}]}, "8": {"loc": {"start": {"line": 63, "column": 17}, "end": {"line": 63, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 17}, "end": {"line": 63, "column": 37}}, {"start": {"line": 63, "column": 35}, "end": {"line": 63, "column": 37}}]}, "9": {"loc": {"start": {"line": 71, "column": 2}, "end": {"line": 79, "column": 3}}, "type": "if", "locations": [{"start": {"line": 71, "column": 2}, "end": {"line": 79, "column": 3}}]}, "10": {"loc": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 11}}, {"start": {"line": 71, "column": 15}, "end": {"line": 71, "column": 68}}]}, "11": {"loc": {"start": {"line": 83, "column": 11}, "end": {"line": 83, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 11}, "end": {"line": 83, "column": 22}}, {"start": {"line": 83, "column": 26}, "end": {"line": 83, "column": 28}}]}, "12": {"loc": {"start": {"line": 95, "column": 2}, "end": {"line": 106, "column": 3}}, "type": "if", "locations": [{"start": {"line": 95, "column": 2}, "end": {"line": 106, "column": 3}}]}, "13": {"loc": {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 32}}, "type": "cond-expr", "locations": [{"start": {"line": 102, "column": 28}, "end": {"line": 102, "column": 30}}, {"start": {"line": 102, "column": 28}, "end": {"line": 102, "column": 32}}]}, "14": {"loc": {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 30}}, {"start": {"line": 102, "column": 28}, "end": {"line": 102, "column": 30}}]}, "15": {"loc": {"start": {"line": 103, "column": 20}, "end": {"line": 103, "column": 43}}, "type": "cond-expr", "locations": [{"start": {"line": 103, "column": 38}, "end": {"line": 103, "column": 40}}, {"start": {"line": 103, "column": 38}, "end": {"line": 103, "column": 43}}]}, "16": {"loc": {"start": {"line": 103, "column": 20}, "end": {"line": 103, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 20}, "end": {"line": 103, "column": 40}}, {"start": {"line": 103, "column": 38}, "end": {"line": 103, "column": 40}}]}, "17": {"loc": {"start": {"line": 117, "column": 2}, "end": {"line": 128, "column": 3}}, "type": "if", "locations": [{"start": {"line": 117, "column": 2}, "end": {"line": 128, "column": 3}}]}, "18": {"loc": {"start": {"line": 124, "column": 10}, "end": {"line": 124, "column": 32}}, "type": "cond-expr", "locations": [{"start": {"line": 124, "column": 28}, "end": {"line": 124, "column": 30}}, {"start": {"line": 124, "column": 28}, "end": {"line": 124, "column": 32}}]}, "19": {"loc": {"start": {"line": 124, "column": 10}, "end": {"line": 124, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 124, "column": 10}, "end": {"line": 124, "column": 30}}, {"start": {"line": 124, "column": 28}, "end": {"line": 124, "column": 30}}]}, "20": {"loc": {"start": {"line": 125, "column": 20}, "end": {"line": 125, "column": 43}}, "type": "cond-expr", "locations": [{"start": {"line": 125, "column": 38}, "end": {"line": 125, "column": 40}}, {"start": {"line": 125, "column": 38}, "end": {"line": 125, "column": 43}}]}, "21": {"loc": {"start": {"line": 125, "column": 20}, "end": {"line": 125, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 125, "column": 20}, "end": {"line": 125, "column": 40}}, {"start": {"line": 125, "column": 38}, "end": {"line": 125, "column": 40}}]}, "22": {"loc": {"start": {"line": 154, "column": 2}, "end": {"line": 156, "column": 3}}, "type": "if", "locations": [{"start": {"line": 154, "column": 2}, "end": {"line": 156, "column": 3}}]}, "23": {"loc": {"start": {"line": 158, "column": 2}, "end": {"line": 168, "column": 3}}, "type": "if", "locations": [{"start": {"line": 158, "column": 2}, "end": {"line": 168, "column": 3}}]}, "24": {"loc": {"start": {"line": 165, "column": 10}, "end": {"line": 165, "column": 32}}, "type": "cond-expr", "locations": [{"start": {"line": 165, "column": 28}, "end": {"line": 165, "column": 30}}, {"start": {"line": 165, "column": 28}, "end": {"line": 165, "column": 32}}]}, "25": {"loc": {"start": {"line": 165, "column": 10}, "end": {"line": 165, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 165, "column": 10}, "end": {"line": 165, "column": 30}}, {"start": {"line": 165, "column": 28}, "end": {"line": 165, "column": 30}}]}, "26": {"loc": {"start": {"line": 180, "column": 2}, "end": {"line": 180, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 180, "column": 26}, "end": {"line": 180, "column": 37}}]}, "27": {"loc": {"start": {"line": 185, "column": 2}, "end": {"line": 187, "column": 3}}, "type": "if", "locations": [{"start": {"line": 185, "column": 2}, "end": {"line": 187, "column": 3}}]}, "28": {"loc": {"start": {"line": 190, "column": 2}, "end": {"line": 201, "column": 3}}, "type": "if", "locations": [{"start": {"line": 190, "column": 2}, "end": {"line": 201, "column": 3}}]}, "29": {"loc": {"start": {"line": 198, "column": 10}, "end": {"line": 198, "column": 32}}, "type": "cond-expr", "locations": [{"start": {"line": 198, "column": 28}, "end": {"line": 198, "column": 30}}, {"start": {"line": 198, "column": 28}, "end": {"line": 198, "column": 32}}]}, "30": {"loc": {"start": {"line": 198, "column": 10}, "end": {"line": 198, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 198, "column": 10}, "end": {"line": 198, "column": 30}}, {"start": {"line": 198, "column": 28}, "end": {"line": 198, "column": 30}}]}, "31": {"loc": {"start": {"line": 238, "column": 11}, "end": {"line": 238, "column": 28}}, "type": "cond-expr", "locations": [{"start": {"line": 238, "column": 23}, "end": {"line": 238, "column": 25}}, {"start": {"line": 238, "column": 23}, "end": {"line": 238, "column": 28}}]}, "32": {"loc": {"start": {"line": 238, "column": 11}, "end": {"line": 238, "column": 25}}, "type": "binary-expr", "locations": [{"start": {"line": 238, "column": 11}, "end": {"line": 238, "column": 25}}, {"start": {"line": 238, "column": 23}, "end": {"line": 238, "column": 25}}]}, "33": {"loc": {"start": {"line": 239, "column": 13}, "end": {"line": 239, "column": 39}}, "type": "cond-expr", "locations": [{"start": {"line": 239, "column": 32}, "end": {"line": 239, "column": 34}}, {"start": {"line": 239, "column": 32}, "end": {"line": 239, "column": 39}}]}, "34": {"loc": {"start": {"line": 239, "column": 13}, "end": {"line": 239, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 239, "column": 13}, "end": {"line": 239, "column": 34}}, {"start": {"line": 239, "column": 32}, "end": {"line": 239, "column": 34}}]}, "35": {"loc": {"start": {"line": 239, "column": 13}, "end": {"line": 239, "column": 32}}, "type": "cond-expr", "locations": [{"start": {"line": 239, "column": 25}, "end": {"line": 239, "column": 27}}, {"start": {"line": 239, "column": 25}, "end": {"line": 239, "column": 32}}]}, "36": {"loc": {"start": {"line": 239, "column": 13}, "end": {"line": 239, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 239, "column": 13}, "end": {"line": 239, "column": 27}}, {"start": {"line": 239, "column": 25}, "end": {"line": 239, "column": 27}}]}, "37": {"loc": {"start": {"line": 240, "column": 13}, "end": {"line": 240, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 240, "column": 38}, "end": {"line": 240, "column": 51}}, {"start": {"line": 240, "column": 54}, "end": {"line": 240, "column": 71}}]}, "38": {"loc": {"start": {"line": 243, "column": 10}, "end": {"line": 243, "column": 32}}, "type": "cond-expr", "locations": [{"start": {"line": 243, "column": 28}, "end": {"line": 243, "column": 30}}, {"start": {"line": 243, "column": 28}, "end": {"line": 243, "column": 32}}]}, "39": {"loc": {"start": {"line": 243, "column": 10}, "end": {"line": 243, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 243, "column": 10}, "end": {"line": 243, "column": 30}}, {"start": {"line": 243, "column": 28}, "end": {"line": 243, "column": 30}}]}, "40": {"loc": {"start": {"line": 257, "column": 17}, "end": {"line": 257, "column": 89}}, "type": "cond-expr", "locations": [{"start": {"line": 257, "column": 32}, "end": {"line": 257, "column": 66}}, {"start": {"line": 257, "column": 69}, "end": {"line": 257, "column": 89}}]}, "41": {"loc": {"start": {"line": 269, "column": 15}, "end": {"line": 269, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 269, "column": 40}, "end": {"line": 269, "column": 53}}, {"start": {"line": 269, "column": 56}, "end": {"line": 269, "column": 73}}]}, "42": {"loc": {"start": {"line": 289, "column": 17}, "end": {"line": 289, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 289, "column": 18}, "end": {"line": 289, "column": 60}}, {"start": {"line": 289, "column": 64}, "end": {"line": 289, "column": 77}}]}, "43": {"loc": {"start": {"line": 289, "column": 18}, "end": {"line": 289, "column": 60}}, "type": "cond-expr", "locations": [{"start": {"line": 289, "column": 41}, "end": {"line": 289, "column": 43}}, {"start": {"line": 289, "column": 41}, "end": {"line": 289, "column": 60}}]}, "44": {"loc": {"start": {"line": 289, "column": 18}, "end": {"line": 289, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 289, "column": 18}, "end": {"line": 289, "column": 43}}, {"start": {"line": 289, "column": 41}, "end": {"line": 289, "column": 43}}]}, "45": {"loc": {"start": {"line": 293, "column": 13}, "end": {"line": 293, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 293, "column": 13}, "end": {"line": 293, "column": 29}}, {"start": {"line": 293, "column": 33}, "end": {"line": 293, "column": 35}}]}, "46": {"loc": {"start": {"line": 301, "column": 13}, "end": {"line": 301, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 301, "column": 38}, "end": {"line": 301, "column": 51}}, {"start": {"line": 301, "column": 54}, "end": {"line": 301, "column": 71}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0], "23": [0], "24": [0, 0], "25": [0, 0], "26": [0], "27": [0], "28": [0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0], "41": [0, 0], "42": [0, 0], "43": [0, 0], "44": [0, 0], "45": [0, 0], "46": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\validation.ts": {"path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\src\\firebase\\functions\\src\\utils\\validation.ts", "statementMap": {"0": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 7}}, "1": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 7}}, "2": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 7}}, "3": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 16}}, "4": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 16}}, "5": {"start": {"line": 239, "column": 0}, "end": {"line": 239, "column": 16}}, "6": {"start": {"line": 259, "column": 0}, "end": {"line": 259, "column": 16}}, "7": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 24}}, "8": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 56}}, "9": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 44}}, "10": {"start": {"line": 14, "column": 13}, "end": {"line": 46, "column": 2}}, "11": {"start": {"line": 18, "column": 14}, "end": {"line": 18, "column": 38}}, "12": {"start": {"line": 22, "column": 14}, "end": {"line": 22, "column": 38}}, "13": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 65}}, "14": {"start": {"line": 49, "column": 13}, "end": {"line": 61, "column": 3}}, "15": {"start": {"line": 64, "column": 13}, "end": {"line": 76, "column": 3}}, "16": {"start": {"line": 79, "column": 13}, "end": {"line": 105, "column": 3}}, "17": {"start": {"line": 85, "column": 14}, "end": {"line": 85, "column": 38}}, "18": {"start": {"line": 115, "column": 13}, "end": {"line": 115, "column": 27}}, "19": {"start": {"line": 116, "column": 21}, "end": {"line": 116, "column": 73}}, "20": {"start": {"line": 118, "column": 2}, "end": {"line": 120, "column": 3}}, "21": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 43}}, "22": {"start": {"line": 122, "column": 22}, "end": {"line": 122, "column": 40}}, "23": {"start": {"line": 125, "column": 2}, "end": {"line": 129, "column": 3}}, "24": {"start": {"line": 126, "column": 4}, "end": {"line": 128, "column": 5}}, "25": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 85}}, "26": {"start": {"line": 131, "column": 2}, "end": {"line": 131, "column": 20}}, "27": {"start": {"line": 141, "column": 13}, "end": {"line": 141, "column": 27}}, "28": {"start": {"line": 142, "column": 19}, "end": {"line": 142, "column": 67}}, "29": {"start": {"line": 144, "column": 2}, "end": {"line": 146, "column": 3}}, "30": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 52}}, "31": {"start": {"line": 148, "column": 20}, "end": {"line": 148, "column": 36}}, "32": {"start": {"line": 150, "column": 2}, "end": {"line": 152, "column": 3}}, "33": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 54}}, "34": {"start": {"line": 154, "column": 2}, "end": {"line": 156, "column": 3}}, "35": {"start": {"line": 155, "column": 4}, "end": {"line": 155, "column": 126}}, "36": {"start": {"line": 158, "column": 2}, "end": {"line": 158, "column": 18}}, "37": {"start": {"line": 167, "column": 13}, "end": {"line": 167, "column": 27}}, "38": {"start": {"line": 168, "column": 20}, "end": {"line": 168, "column": 70}}, "39": {"start": {"line": 170, "column": 2}, "end": {"line": 172, "column": 3}}, "40": {"start": {"line": 171, "column": 4}, "end": {"line": 171, "column": 54}}, "41": {"start": {"line": 174, "column": 21}, "end": {"line": 174, "column": 38}}, "42": {"start": {"line": 176, "column": 2}, "end": {"line": 178, "column": 3}}, "43": {"start": {"line": 177, "column": 4}, "end": {"line": 177, "column": 56}}, "44": {"start": {"line": 180, "column": 2}, "end": {"line": 182, "column": 3}}, "45": {"start": {"line": 181, "column": 4}, "end": {"line": 181, "column": 57}}, "46": {"start": {"line": 184, "column": 2}, "end": {"line": 184, "column": 19}}, "47": {"start": {"line": 191, "column": 17}, "end": {"line": 191, "column": 37}}, "48": {"start": {"line": 192, "column": 17}, "end": {"line": 192, "column": 37}}, "49": {"start": {"line": 193, "column": 21}, "end": {"line": 193, "column": 31}}, "50": {"start": {"line": 195, "column": 2}, "end": {"line": 197, "column": 3}}, "51": {"start": {"line": 196, "column": 4}, "end": {"line": 196, "column": 72}}, "52": {"start": {"line": 199, "column": 2}, "end": {"line": 201, "column": 3}}, "53": {"start": {"line": 200, "column": 4}, "end": {"line": 200, "column": 75}}, "54": {"start": {"line": 203, "column": 19}, "end": {"line": 203, "column": 44}}, "55": {"start": {"line": 204, "column": 2}, "end": {"line": 206, "column": 3}}, "56": {"start": {"line": 205, "column": 4}, "end": {"line": 205, "column": 69}}, "57": {"start": {"line": 217, "column": 53}, "end": {"line": 223, "column": 4}}, "58": {"start": {"line": 225, "column": 2}, "end": {"line": 227, "column": 3}}, "59": {"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": 86}}, "60": {"start": {"line": 230, "column": 32}, "end": {"line": 230, "column": 73}}, "61": {"start": {"line": 231, "column": 2}, "end": {"line": 233, "column": 3}}, "62": {"start": {"line": 232, "column": 4}, "end": {"line": 232, "column": 85}}, "63": {"start": {"line": 247, "column": 2}, "end": {"line": 253, "column": 5}}, "64": {"start": {"line": 260, "column": 2}, "end": {"line": 262, "column": 3}}, "65": {"start": {"line": 261, "column": 4}, "end": {"line": 261, "column": 23}}, "66": {"start": {"line": 264, "column": 2}, "end": {"line": 266, "column": 3}}, "67": {"start": {"line": 265, "column": 4}, "end": {"line": 265, "column": 35}}, "68": {"start": {"line": 268, "column": 2}, "end": {"line": 274, "column": 3}}, "69": {"start": {"line": 269, "column": 27}, "end": {"line": 269, "column": 29}}, "70": {"start": {"line": 270, "column": 4}, "end": {"line": 272, "column": 5}}, "71": {"start": {"line": 271, "column": 6}, "end": {"line": 271, "column": 44}}, "72": {"start": {"line": 273, "column": 4}, "end": {"line": 273, "column": 21}}, "73": {"start": {"line": 276, "column": 2}, "end": {"line": 276, "column": 14}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 5}}, "loc": {"start": {"line": 18, "column": 14}, "end": {"line": 18, "column": 38}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 5}}, "loc": {"start": {"line": 22, "column": 14}, "end": {"line": 22, "column": 38}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 3}}, "loc": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 65}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": 5}}, "loc": {"start": {"line": 85, "column": 14}, "end": {"line": 85, "column": 38}}}, "4": {"name": "validateEmpruntAccess", "decl": {"start": {"line": 110, "column": 22}, "end": {"line": 110, "column": 43}}, "loc": {"start": {"line": 113, "column": 35}, "end": {"line": 132, "column": 1}}}, "5": {"name": "validateStockAvailability", "decl": {"start": {"line": 137, "column": 22}, "end": {"line": 137, "column": 47}}, "loc": {"start": {"line": 139, "column": 25}, "end": {"line": 159, "column": 1}}}, "6": {"name": "validateModuleAvailability", "decl": {"start": {"line": 164, "column": 22}, "end": {"line": 164, "column": 48}}, "loc": {"start": {"line": 165, "column": 18}, "end": {"line": 185, "column": 1}}}, "7": {"name": "validateEmpruntDates", "decl": {"start": {"line": 190, "column": 16}, "end": {"line": 190, "column": 36}}, "loc": {"start": {"line": 190, "column": 75}, "end": {"line": 207, "column": 1}}}, "8": {"name": "validateEmpruntStatusTransition", "decl": {"start": {"line": 212, "column": 16}, "end": {"line": 212, "column": 47}}, "loc": {"start": {"line": 215, "column": 18}, "end": {"line": 234, "column": 1}}}, "9": {"name": "logSimpleValidationError", "decl": {"start": {"line": 239, "column": 16}, "end": {"line": 239, "column": 40}}, "loc": {"start": {"line": 245, "column": 3}, "end": {"line": 254, "column": 1}}}, "10": {"name": "sanitizeInput", "decl": {"start": {"line": 259, "column": 16}, "end": {"line": 259, "column": 29}}, "loc": {"start": {"line": 259, "column": 39}, "end": {"line": 277, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 113, "column": 30}, "end": {"line": 113, "column": 35}}]}, "1": {"loc": {"start": {"line": 118, "column": 2}, "end": {"line": 120, "column": 3}}, "type": "if", "locations": [{"start": {"line": 118, "column": 2}, "end": {"line": 120, "column": 3}}]}, "2": {"loc": {"start": {"line": 125, "column": 2}, "end": {"line": 129, "column": 3}}, "type": "if", "locations": [{"start": {"line": 125, "column": 2}, "end": {"line": 129, "column": 3}}]}, "3": {"loc": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 22}}, {"start": {"line": 125, "column": 26}, "end": {"line": 125, "column": 47}}, {"start": {"line": 125, "column": 51}, "end": {"line": 125, "column": 76}}]}, "4": {"loc": {"start": {"line": 126, "column": 4}, "end": {"line": 128, "column": 5}}, "type": "if", "locations": [{"start": {"line": 126, "column": 4}, "end": {"line": 128, "column": 5}}]}, "5": {"loc": {"start": {"line": 144, "column": 2}, "end": {"line": 146, "column": 3}}, "type": "if", "locations": [{"start": {"line": 144, "column": 2}, "end": {"line": 146, "column": 3}}]}, "6": {"loc": {"start": {"line": 150, "column": 2}, "end": {"line": 152, "column": 3}}, "type": "if", "locations": [{"start": {"line": 150, "column": 2}, "end": {"line": 152, "column": 3}}]}, "7": {"loc": {"start": {"line": 154, "column": 2}, "end": {"line": 156, "column": 3}}, "type": "if", "locations": [{"start": {"line": 154, "column": 2}, "end": {"line": 156, "column": 3}}]}, "8": {"loc": {"start": {"line": 170, "column": 2}, "end": {"line": 172, "column": 3}}, "type": "if", "locations": [{"start": {"line": 170, "column": 2}, "end": {"line": 172, "column": 3}}]}, "9": {"loc": {"start": {"line": 176, "column": 2}, "end": {"line": 178, "column": 3}}, "type": "if", "locations": [{"start": {"line": 176, "column": 2}, "end": {"line": 178, "column": 3}}]}, "10": {"loc": {"start": {"line": 180, "column": 2}, "end": {"line": 182, "column": 3}}, "type": "if", "locations": [{"start": {"line": 180, "column": 2}, "end": {"line": 182, "column": 3}}]}, "11": {"loc": {"start": {"line": 195, "column": 2}, "end": {"line": 197, "column": 3}}, "type": "if", "locations": [{"start": {"line": 195, "column": 2}, "end": {"line": 197, "column": 3}}]}, "12": {"loc": {"start": {"line": 199, "column": 2}, "end": {"line": 201, "column": 3}}, "type": "if", "locations": [{"start": {"line": 199, "column": 2}, "end": {"line": 201, "column": 3}}]}, "13": {"loc": {"start": {"line": 204, "column": 2}, "end": {"line": 206, "column": 3}}, "type": "if", "locations": [{"start": {"line": 204, "column": 2}, "end": {"line": 206, "column": 3}}]}, "14": {"loc": {"start": {"line": 225, "column": 2}, "end": {"line": 227, "column": 3}}, "type": "if", "locations": [{"start": {"line": 225, "column": 2}, "end": {"line": 227, "column": 3}}]}, "15": {"loc": {"start": {"line": 225, "column": 7}, "end": {"line": 225, "column": 59}}, "type": "cond-expr", "locations": [{"start": {"line": 225, "column": 38}, "end": {"line": 225, "column": 40}}, {"start": {"line": 225, "column": 38}, "end": {"line": 225, "column": 59}}]}, "16": {"loc": {"start": {"line": 225, "column": 7}, "end": {"line": 225, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 225, "column": 7}, "end": {"line": 225, "column": 40}}, {"start": {"line": 225, "column": 38}, "end": {"line": 225, "column": 40}}]}, "17": {"loc": {"start": {"line": 231, "column": 2}, "end": {"line": 233, "column": 3}}, "type": "if", "locations": [{"start": {"line": 231, "column": 2}, "end": {"line": 233, "column": 3}}]}, "18": {"loc": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 93}}, "type": "binary-expr", "locations": [{"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 47}}, {"start": {"line": 231, "column": 51}, "end": {"line": 231, "column": 93}}]}, "19": {"loc": {"start": {"line": 251, "column": 10}, "end": {"line": 251, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 251, "column": 25}, "end": {"line": 251, "column": 53}}, {"start": {"line": 251, "column": 56}, "end": {"line": 251, "column": 65}}]}, "20": {"loc": {"start": {"line": 260, "column": 2}, "end": {"line": 262, "column": 3}}, "type": "if", "locations": [{"start": {"line": 260, "column": 2}, "end": {"line": 262, "column": 3}}]}, "21": {"loc": {"start": {"line": 264, "column": 2}, "end": {"line": 266, "column": 3}}, "type": "if", "locations": [{"start": {"line": 264, "column": 2}, "end": {"line": 266, "column": 3}}]}, "22": {"loc": {"start": {"line": 268, "column": 2}, "end": {"line": 274, "column": 3}}, "type": "if", "locations": [{"start": {"line": 268, "column": 2}, "end": {"line": 274, "column": 3}}]}, "23": {"loc": {"start": {"line": 268, "column": 6}, "end": {"line": 268, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 268, "column": 6}, "end": {"line": 268, "column": 10}}, {"start": {"line": 268, "column": 14}, "end": {"line": 268, "column": 38}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0, 0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0], "12": [0], "13": [0], "14": [0], "15": [0, 0], "16": [0, 0], "17": [0], "18": [0, 0], "19": [0, 0], "20": [0], "21": [0], "22": [0], "23": [0, 0]}}}