/**
 * Configuration Jest pour les Cloud Functions SIGMA (émulateurs).
 */
module.exports = {
  testEnvironment: 'node',
  moduleFileExtensions: ['ts', 'js'],
  transform: {
    '^.+\\.ts$': ['ts-jest', { tsconfig: 'tsconfig.json' }],
  },

  // Exécute env AVANT tout (variables d'env des émulateurs)
  setupFiles: ['<rootDir>/src/tests/env.ts'],
  // Initialise l'Admin SDK APRÈS l'env
  setupFilesAfterEnv: ['<rootDir>/src/tests/setup.ts'],

  // IMPORTANT : ne teste QUE les sources TypeScript
  testMatch: ['**/src/tests/**/*.test.ts'],
  // Et ignore les sorties compilées pour éviter l'exécution en double
  testPathIgnorePatterns: ['/node_modules/', '/lib/', '/dist/'],

  testTimeout: 30000,
  verbose: true,
  forceExit: true,
  clearMocks: true,
  restoreMocks: true,
};
