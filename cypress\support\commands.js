// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Commande pour se connecter en tant que régisseur
Cypress.Commands.add('loginAsRegisseur', () => {
  cy.window().then((win) => {
    win.localStorage.setItem('user', JSON.stringify({
      uid: 'regisseur-test',
      role: 'regisseur',
      email: '<EMAIL>'
    }))
  })
})

// Commande pour se connecter en tant qu'admin
Cypress.Commands.add('loginAsAdmin', () => {
  cy.window().then((win) => {
    win.localStorage.setItem('user', JSON.stringify({
      uid: 'admin-test',
      role: 'admin',
      email: '<EMAIL>'
    }))
  })
})

// Commande pour créer un emprunt de test
Cypress.Commands.add('createTestEmprunt', (empruntData = {}) => {
  const defaultData = {
    nom: 'Test Manipulation',
    lieu: 'Salle de test',
    dateDepart: '2024-12-01',
    dateRetourPrevue: '2024-12-05',
    secteur: 'Test Secteur',
    referent: 'Test Référent',
    emprunteur: 'Test Emprunteur',
    ...empruntData
  }

  cy.window().then((win) => {
    return win.firebase.functions().httpsCallable('createEmprunt')(defaultData)
  })
})

// Commande pour attendre le chargement de la page
Cypress.Commands.add('waitForPageLoad', () => {
  cy.get('[data-cy=page-loader]', { timeout: 10000 }).should('not.exist')
})

// Commande pour vérifier les permissions
Cypress.Commands.add('checkPermissions', (expectedRole) => {
  cy.window().then((win) => {
    const user = JSON.parse(win.localStorage.getItem('user') || '{}')
    expect(user.role).to.equal(expectedRole)
  })
})

// Commande pour simuler une erreur réseau
Cypress.Commands.add('simulateNetworkError', () => {
  cy.intercept('**', { forceNetworkError: true }).as('networkError')
})

// Commande pour remplir le formulaire d'emprunt
Cypress.Commands.add('fillEmpruntForm', (data) => {
  if (data.nom) cy.get('[data-cy=nom-manipulation]').clear().type(data.nom)
  if (data.lieu) cy.get('[data-cy=lieu]').clear().type(data.lieu)
  if (data.dateDepart) cy.get('[data-cy=date-depart]').clear().type(data.dateDepart)
  if (data.dateRetourPrevue) cy.get('[data-cy=date-retour]').clear().type(data.dateRetourPrevue)
  if (data.secteur) cy.get('[data-cy=secteur]').select(data.secteur)
  if (data.referent) cy.get('[data-cy=referent]').clear().type(data.referent)
  if (data.emprunteur) cy.get('[data-cy=emprunteur]').clear().type(data.emprunteur)
  if (data.notes) cy.get('[data-cy=notes]').clear().type(data.notes)
})
